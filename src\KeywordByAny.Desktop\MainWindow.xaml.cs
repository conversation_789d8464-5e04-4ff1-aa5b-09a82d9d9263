using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Hardcodet.Wpf.TaskbarNotification;
using KeywordByAny.Desktop.Services;
using KeywordByAny.Desktop.Models;
using KeywordByAny.Desktop.Views;

namespace KeywordByAny.Desktop
{
    public partial class MainWindow : Window
    {
        private WebSocketServer _webSocketServer;
        private InputSimulator _inputSimulator;
        private SettingsService _settingsService;
        private PairingCodeService _pairingService;
        private TaskbarIcon _taskbarIcon;
        private int _connectedClients = 0;

        public MainWindow()
        {
            try
            {
                InitializeComponent();
                InitializeServices();
                InitializeTaskbarIcon();
                LoadSettings();
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序初始化失败: {ex.Message}\n\n详细信息: {ex}",
                    "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeServices()
        {
            try
            {
                _settingsService = new SettingsService();
                _inputSimulator = new InputSimulator(_settingsService.Settings);
                _pairingService = new PairingCodeService();
                _webSocketServer = new WebSocketServer(_inputSimulator, _settingsService.Settings);

                // 订阅事件
                _webSocketServer.OnLog += OnServerLog;
                _webSocketServer.OnClientConnected += OnClientConnected;
                _webSocketServer.OnClientDisconnected += OnClientDisconnected;
                _settingsService.SettingsChanged += OnSettingsChanged;
                _pairingService.OnPairingSuccess += OnPairingSuccess;
            }
            catch (Exception ex)
            {
                throw new Exception($"服务初始化失败: {ex.Message}", ex);
            }
        }

        private void InitializeTaskbarIcon()
        {
            try
            {
                _taskbarIcon = new TaskbarIcon
                {
                    ToolTipText = "KeywordByAny - 无线触控板和键盘"
                };

                _taskbarIcon.TrayLeftMouseDown += (s, e) =>
                {
                    Show();
                    WindowState = WindowState.Normal;
                    Activate();
                };
            }
            catch (Exception ex)
            {
                // 如果托盘图标初始化失败，记录错误但不阻止应用启动
                System.Diagnostics.Debug.WriteLine($"托盘图标初始化失败: {ex.Message}");
            }
        }

        private void LoadSettings()
        {
            var settings = _settingsService.Settings;

            PortTextBox.Text = settings.Port.ToString();
            StartWithWindowsCheckBox.IsChecked = settings.StartWithWindows;
            MinimizeToTrayCheckBox.IsChecked = settings.MinimizeToTray;
        }

        private void SaveCurrentSettings()
        {
            _settingsService.UpdateSettings(settings =>
            {
                if (int.TryParse(PortTextBox.Text, out int port))
                    settings.Port = port;

                settings.StartWithWindows = StartWithWindowsCheckBox.IsChecked ?? false;
                settings.MinimizeToTray = MinimizeToTrayCheckBox.IsChecked ?? true;
            });
        }

        private void UpdateUI()
        {
            bool isRunning = _webSocketServer?.IsRunning == true;

            StartStopButton.Content = isRunning ? "🛑 停止服务器" : "🚀 启动服务器";
            StatusText.Text = isRunning ? "服务器运行中" : "服务器已停止";
            PortTextBox.IsEnabled = !isRunning;

            // 更新状态指示器
            if (StatusIndicator != null)
            {
                StatusIndicator.Fill = isRunning ?
                    (System.Windows.Media.Brush)FindResource("AccentSuccess") :
                    (System.Windows.Media.Brush)FindResource("AccentDanger");
            }

            if (isRunning)
            {
                IpAddressText.Text = $"http://{GetLocalIPAddress()}:{_settingsService.Settings.Port}";
            }
            else
            {
                IpAddressText.Text = "未启动";
            }

            ConnectedClientsText.Text = _connectedClients.ToString();
        }

        private string GetLocalIPAddress()
        {
            try
            {
                using var socket = new System.Net.Sockets.Socket(System.Net.Sockets.AddressFamily.InterNetwork,
                    System.Net.Sockets.SocketType.Dgram, 0);
                socket.Connect("*******", 65530);
                return ((System.Net.IPEndPoint)socket.LocalEndPoint).Address.ToString();
            }
            catch
            {
                return "localhost";
            }
        }

        #region Event Handlers

        private async void StartStopButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_webSocketServer.IsRunning)
                {
                    _webSocketServer.Stop();
                }
                else
                {
                    SaveCurrentSettings();
                    await _webSocketServer.StartAsync();
                }
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                OnServerLog($"操作失败: {ex.Message}");
            }
        }

        private void ApplyPortButton_Click(object sender, RoutedEventArgs e)
        {
            SaveCurrentSettings();
            OnServerLog("端口设置已保存");
        }

        private void OpenWebButton_Click(object sender, RoutedEventArgs e)
        {
            if (_webSocketServer.IsRunning)
            {
                string url = $"http://{GetLocalIPAddress()}:{_settingsService.Settings.Port}";
                Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
            }
            else
            {
                MessageBox.Show("请先启动服务器", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void GenerateQRButton_Click(object sender, RoutedEventArgs e)
        {
            if (_webSocketServer.IsRunning)
            {
                try
                {
                    string url = $"http://{GetLocalIPAddress()}:{_settingsService.Settings.Port}";
                    var qrWindow = new QRCodeWindow(url, _pairingService)
                    {
                        Owner = this
                    };
                    qrWindow.ShowDialog();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"打开二维码窗口失败: {ex.Message}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("请先启动服务器", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CopyAddressButton_Click(object sender, RoutedEventArgs e)
        {
            if (_webSocketServer.IsRunning)
            {
                string url = $"http://{GetLocalIPAddress()}:{_settingsService.Settings.Port}";
                Clipboard.SetText(url);
                OnServerLog("地址已复制到剪贴板");
            }
            else
            {
                MessageBox.Show("请先启动服务器", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ResetSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有设置为默认值吗？", "确认",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _settingsService.ResetToDefaults();
                LoadSettings();
                OnServerLog("设置已重置为默认值");
            }
        }

        private void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            SaveCurrentSettings();
            OnServerLog("设置已保存");
        }

        private void VisitHomepageButton_Click(object sender, RoutedEventArgs e)
        {
            Process.Start(new ProcessStartInfo("https://github.com/your-username/keywordByAny")
            { UseShellExecute = true });
        }

        private void Window_StateChanged(object sender, EventArgs e)
        {
            if (WindowState == WindowState.Minimized && _settingsService.Settings.MinimizeToTray)
            {
                Hide();
            }
        }

        private void OnServerLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                var logEntry = $"[{timestamp}] {message}\n";

                // 限制日志长度，避免内存占用过多
                if (LogTextBlock.Text.Length > 10000)
                {
                    var lines = LogTextBlock.Text.Split('\n');
                    LogTextBlock.Text = string.Join("\n", lines.Skip(lines.Length / 2));
                }

                LogTextBlock.Text += logEntry;

                // 自动滚动到底部
                if (LogTextBlock.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToEnd();
                }
            });
        }

        private void OnClientConnected(string clientId)
        {
            Dispatcher.Invoke(() =>
            {
                _connectedClients++;
                UpdateUI();
                OnServerLog($"设备已连接: {clientId}");
            });
        }

        private void OnClientDisconnected(string clientId)
        {
            Dispatcher.Invoke(() =>
            {
                _connectedClients--;
                UpdateUI();
                OnServerLog($"设备已断开: {clientId}");
            });
        }

        private void OnSettingsChanged(AppSettings settings)
        {
            // 设置变化时的处理
        }

        private void OnPairingSuccess(string clientId)
        {
            Dispatcher.Invoke(() =>
            {
                OnServerLog($"设备配对成功: {clientId}");
            });
        }

        #endregion

        protected override void OnClosed(EventArgs e)
        {
            _webSocketServer?.Stop();
            _pairingService?.Dispose();
            _taskbarIcon?.Dispose();
            base.OnClosed(e);
        }
    }
}
