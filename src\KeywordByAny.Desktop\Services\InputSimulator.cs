using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using KeywordByAny.Desktop.Models;

namespace KeywordByAny.Desktop.Services
{
    /// <summary>
    /// Windows输入模拟服务
    /// </summary>
    public class InputSimulator
    {
        #region Windows API

        [DllImport("user32.dll")]
        private static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [DllImport("user32.dll")]
        private static extern short VkKeyScan(char ch);

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct INPUT
        {
            public uint Type;
            public INPUTUNION Data;
        }

        [StructLayout(LayoutKind.Explicit)]
        public struct INPUTUNION
        {
            [FieldOffset(0)]
            public MOUSEINPUT Mouse;
            [FieldOffset(0)]
            public KEYBDINPUT Keyboard;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MOUSEINPUT
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        // 输入类型常量
        private const uint INPUT_MOUSE = 0;
        private const uint INPUT_KEYBOARD = 1;

        // 鼠标事件常量
        private const uint MOUSEEVENTF_MOVE = 0x0001;
        private const uint MOUSEEVENTF_LEFTDOWN = 0x0002;
        private const uint MOUSEEVENTF_LEFTUP = 0x0004;
        private const uint MOUSEEVENTF_RIGHTDOWN = 0x0008;
        private const uint MOUSEEVENTF_RIGHTUP = 0x0010;
        private const uint MOUSEEVENTF_MIDDLEDOWN = 0x0020;
        private const uint MOUSEEVENTF_MIDDLEUP = 0x0040;
        private const uint MOUSEEVENTF_WHEEL = 0x0800;
        private const uint MOUSEEVENTF_ABSOLUTE = 0x8000;

        // 键盘事件常量
        private const uint KEYEVENTF_KEYUP = 0x0002;
        private const uint KEYEVENTF_UNICODE = 0x0004;

        #endregion

        private readonly AppSettings _settings;

        public InputSimulator(AppSettings settings)
        {
            _settings = settings;
        }

        /// <summary>
        /// 移动鼠标
        /// </summary>
        public void MoveMouse(double deltaX, double deltaY)
        {
            GetCursorPos(out POINT currentPos);

            int newX = currentPos.X + (int)(deltaX * _settings.MouseSensitivity);
            int newY = currentPos.Y + (int)(deltaY * _settings.MouseSensitivity);

            // 限制在屏幕范围内
            newX = Math.Max(0, Math.Min(Screen.PrimaryScreen.Bounds.Width - 1, newX));
            newY = Math.Max(0, Math.Min(Screen.PrimaryScreen.Bounds.Height - 1, newY));

            SetCursorPos(newX, newY);
        }

        /// <summary>
        /// 鼠标点击
        /// </summary>
        public void MouseClick(MouseButton button)
        {
            uint downFlag, upFlag;

            switch (button)
            {
                case MouseButton.Left:
                    downFlag = MOUSEEVENTF_LEFTDOWN;
                    upFlag = MOUSEEVENTF_LEFTUP;
                    break;
                case MouseButton.Right:
                    downFlag = MOUSEEVENTF_RIGHTDOWN;
                    upFlag = MOUSEEVENTF_RIGHTUP;
                    break;
                case MouseButton.Middle:
                    downFlag = MOUSEEVENTF_MIDDLEDOWN;
                    upFlag = MOUSEEVENTF_MIDDLEUP;
                    break;
                default:
                    return;
            }

            SendMouseInput(downFlag);
            SendMouseInput(upFlag);
        }

        /// <summary>
        /// 鼠标滚轮
        /// </summary>
        public void MouseScroll(double deltaY)
        {
            int scrollAmount = (int)(deltaY * _settings.ScrollSensitivity * 120);
            SendMouseInput(MOUSEEVENTF_WHEEL, (uint)scrollAmount);
        }

        /// <summary>
        /// 发送按键
        /// </summary>
        public void SendKey(Keys key, bool keyUp = false)
        {
            SendKeyboardInput((ushort)key, keyUp ? KEYEVENTF_KEYUP : 0);
        }

        /// <summary>
        /// 发送文本
        /// </summary>
        public void SendText(string text)
        {
            foreach (char c in text)
            {
                SendUnicodeChar(c);
            }
        }

        /// <summary>
        /// 发送Unicode字符
        /// </summary>
        private void SendUnicodeChar(char c)
        {
            // 按下字符
            SendKeyboardInput(c, KEYEVENTF_UNICODE);
            // 释放字符
            SendKeyboardInput(c, KEYEVENTF_UNICODE | KEYEVENTF_KEYUP);
        }

        /// <summary>
        /// 发送鼠标输入
        /// </summary>
        private void SendMouseInput(uint flags, uint data = 0)
        {
            var input = new INPUT
            {
                Type = INPUT_MOUSE,
                Data = new INPUTUNION
                {
                    Mouse = new MOUSEINPUT
                    {
                        dx = 0,
                        dy = 0,
                        mouseData = data,
                        dwFlags = flags,
                        time = 0,
                        dwExtraInfo = IntPtr.Zero
                    }
                }
            };

            SendInput(1, new[] { input }, Marshal.SizeOf<INPUT>());
        }

        /// <summary>
        /// 发送键盘输入
        /// </summary>
        private void SendKeyboardInput(ushort key, uint flags)
        {
            var input = new INPUT
            {
                Type = INPUT_KEYBOARD,
                Data = new INPUTUNION
                {
                    Keyboard = new KEYBDINPUT
                    {
                        wVk = key,
                        wScan = 0,
                        dwFlags = flags,
                        time = 0,
                        dwExtraInfo = IntPtr.Zero
                    }
                }
            };

            SendInput(1, new[] { input }, Marshal.SizeOf<INPUT>());
        }
    }
}
