using System;
using System.IO;
using System.Net;
using System.Net.WebSockets;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using RemoteControl.Desktop.Models;

namespace RemoteControl.Desktop
{
    public class SimpleServer
    {
        private HttpListener _httpListener;
        private bool _isRunning = false;
        private readonly object _lock = new object();
        private readonly AppSettings _settings;

        public event Action<string> OnLog;
        public event Action OnClientConnected;
        public event Action OnClientDisconnected;

        public bool IsRunning
        {
            get
            {
                lock (_lock)
                {
                    return _isRunning;
                }
            }
        }

        public SimpleServer(AppSettings settings)
        {
            _settings = settings;
        }

        public void Start()
        {
            lock (_lock)
            {
                if (_isRunning) return;

                try
                {
                    _httpListener = new HttpListener();
                    _httpListener.Prefixes.Add($"http://+:{_settings.Port}/");
                    _httpListener.Start();
                    _isRunning = true;

                    OnLog?.Invoke($"服务器已启动，端口: {_settings.Port}");

                    // 启动处理请求的任务
                    Task.Run(HandleRequests);
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"启动服务器失败: {ex.Message}");
                    throw;
                }
            }
        }

        public void Stop()
        {
            lock (_lock)
            {
                if (!_isRunning) return;

                try
                {
                    _isRunning = false;
                    _httpListener?.Stop();
                    _httpListener?.Close();
                    _httpListener = null;

                    OnLog?.Invoke("服务器已停止");
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"停止服务器时出错: {ex.Message}");
                }
            }
        }

        private async Task HandleRequests()
        {
            while (IsRunning)
            {
                try
                {
                    var context = await _httpListener.GetContextAsync();
                    _ = Task.Run(() => ProcessRequest(context));
                }
                catch (ObjectDisposedException)
                {
                    break;
                }
                catch (HttpListenerException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"处理请求时出错: {ex.Message}");
                }
            }
        }

        private async Task ProcessRequest(HttpListenerContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                OnLog?.Invoke($"收到请求: {request.Url} - WebSocket: {request.IsWebSocketRequest}");

                if (request.IsWebSocketRequest)
                {
                    await HandleWebSocketRequest(context);
                }
                else
                {
                    await HandleHttpRequest(context);
                }
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"处理请求失败: {ex.Message}");
            }
        }

        private async Task HandleWebSocketRequest(HttpListenerContext context)
        {
            try
            {
                OnLog?.Invoke("正在处理WebSocket升级请求...");

                var wsContext = await context.AcceptWebSocketAsync(null);
                var webSocket = wsContext.WebSocket;

                OnLog?.Invoke("WebSocket客户端已连接");
                OnClientConnected?.Invoke();

                await HandleWebSocketCommunication(webSocket);

                OnLog?.Invoke("WebSocket客户端已断开");
                OnClientDisconnected?.Invoke();
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"WebSocket处理失败: {ex.Message}");
                OnLog?.Invoke($"错误详情: {ex.StackTrace}");
            }
        }

        private async Task HandleWebSocketCommunication(WebSocket webSocket)
        {
            var buffer = new byte[1024 * 4];

            while (webSocket.State == WebSocketState.Open)
            {
                try
                {
                    var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);

                    if (result.MessageType == WebSocketMessageType.Text)
                    {
                        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        await ProcessWebSocketMessage(message, webSocket);
                    }
                    else if (result.MessageType == WebSocketMessageType.Close)
                    {
                        await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "", CancellationToken.None);
                        break;
                    }
                }
                catch (WebSocketException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"WebSocket通信错误: {ex.Message}");
                    break;
                }
            }
        }

        private async Task ProcessWebSocketMessage(string message, WebSocket webSocket)
        {
            try
            {
                OnLog?.Invoke($"收到消息: {message}");

                var data = JsonConvert.DeserializeObject<dynamic>(message);
                string type = data.type;

                switch (type)
                {
                    case "move":
                        double deltaX = (double)data.deltaX;
                        double deltaY = (double)data.deltaY;
                        InputSimulator.MoveMouse((int)deltaX, (int)deltaY);
                        break;

                    case "click":
                        string button = data.button;
                        if (button == "left")
                            InputSimulator.LeftClick();
                        else if (button == "right")
                            InputSimulator.RightClick();
                        break;

                    case "scroll":
                        double scrollX = (double)data.deltaX;
                        double scrollY = (double)data.deltaY;
                        InputSimulator.Scroll((int)scrollY);
                        break;

                    case "key":
                        string key = data.key;
                        InputSimulator.SendKey(key);
                        break;

                    case "text":
                        string text = data.text;
                        InputSimulator.SendText(text);
                        break;

                    case "bulkText":
                        string bulkText = data.text;
                        InputSimulator.SendBulkText(bulkText);
                        break;

                    case "textDiff":
                        var operations = data.operations;
                        InputSimulator.ProcessTextDiff(operations);
                        break;

                    case "shortcut":
                        string keys = data.keys?.ToString() ?? "";
                        OnLog?.Invoke($"收到快捷键: {keys}");
                        InputSimulator.SendShortcut(keys);
                        break;

                    case "dragStart":
                        InputSimulator.StartDrag();
                        break;

                    case "drag":
                        double dragDeltaX = (double)data.deltaX;
                        double dragDeltaY = (double)data.deltaY;
                        InputSimulator.DragMove((int)dragDeltaX, (int)dragDeltaY);
                        break;

                    case "dragEnd":
                        InputSimulator.EndDrag();
                        break;
                }
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"处理消息失败: {ex.Message}");
            }
        }

        private async Task HandleHttpRequest(HttpListenerContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                string path = request.Url.AbsolutePath;
                if (path == "/" || path == "/index.html")
                {
                    // 返回嵌入的HTML文件
                    var htmlContent = GetEmbeddedResource("RemoteControl.Desktop.Web.index.html");
                    var buffer = Encoding.UTF8.GetBytes(htmlContent);

                    response.ContentType = "text/html; charset=utf-8";
                    response.ContentLength64 = buffer.Length;
                    await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
                }
                else if (path == "/test")
                {
                    // 返回测试页面
                    var testContent = @"<!DOCTYPE html>
<html><head><meta charset='UTF-8'><title>连接测试</title>
<style>body{font-family:monospace;padding:20px;background:#0d1117;color:#f0f6fc;}
.log{background:#21262d;padding:10px;border-radius:5px;margin:10px 0;}
button{padding:10px 20px;margin:5px;background:#238636;color:white;border:none;border-radius:5px;}
.error{color:#da3633;}.success{color:#238636;}.info{color:#00d8ff;}</style></head>
<body><h1>WebSocket连接测试</h1><div><button onclick='testConnection()'>测试连接</button></div>
<div id='log' class='log'></div><script>
let ws=null;function log(m,t='info'){const d=document.getElementById('log');
const time=new Date().toLocaleTimeString();const c=t==='error'?'error':t==='success'?'success':'info';
d.innerHTML+=`<div class='${c}'>[${time}] ${m}</div>`;d.scrollTop=d.scrollHeight;console.log(m);}
function testConnection(){log('测试WebSocket连接...','info');
const protocol=window.location.protocol==='https:'?'wss:':'ws:';
const wsUrl=`${protocol}//${window.location.host}`;log(`连接到: ${wsUrl}`,'info');
if(ws)ws.close();ws=new WebSocket(wsUrl);
ws.onopen=()=>log('✓ 连接成功！','success');
ws.onclose=(e)=>log(`✗ 连接关闭 (${e.code})`,`error`);
ws.onerror=()=>log('✗ 连接错误','error');
ws.onmessage=(e)=>log(`收到: ${e.data}`,'success');
setTimeout(()=>{if(ws.readyState===WebSocket.CONNECTING){log('✗ 超时','error');ws.close();}
else if(ws.readyState===WebSocket.OPEN){log('✓ 正常','success');
ws.send(JSON.stringify({type:'test'}));}},3000);}
window.onload=()=>setTimeout(testConnection,500);</script></body></html>";

                    var buffer = Encoding.UTF8.GetBytes(testContent);
                    response.ContentType = "text/html; charset=utf-8";
                    response.ContentLength64 = buffer.Length;
                    await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
                }
                else
                {
                    response.StatusCode = 404;
                    var notFound = Encoding.UTF8.GetBytes("Not Found");
                    await response.OutputStream.WriteAsync(notFound, 0, notFound.Length);
                }

                response.OutputStream.Close();
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"HTTP请求处理失败: {ex.Message}");
            }
        }

        private string GetEmbeddedResource(string resourceName)
        {
            var assembly = Assembly.GetExecutingAssembly();
            using var stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                return "<html><body><h1>Resource not found</h1></body></html>";
            }
            using var reader = new StreamReader(stream);
            return reader.ReadToEnd();
        }
    }
}
