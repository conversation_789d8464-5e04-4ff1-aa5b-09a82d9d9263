using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Media.Imaging;
using QRCoder;

namespace KeywordByAny.Desktop.Services
{
    /// <summary>
    /// 二维码生成服务
    /// </summary>
    public class QRCodeService
    {
        /// <summary>
        /// 生成二维码图片
        /// </summary>
        /// <param name="text">要编码的文本</param>
        /// <param name="pixelsPerModule">每个模块的像素数</param>
        /// <returns>二维码BitmapImage</returns>
        public static BitmapImage GenerateQRCode(string text, int pixelsPerModule = 10)
        {
            try
            {
                using var qrGenerator = new QRCodeGenerator();
                using var qrCodeData = qrGenerator.CreateQrCode(text, QRCodeGenerator.ECCLevel.M);
                using var qrCode = new QRCode(qrCodeData);
                
                // 生成二维码位图
                using var qrCodeImage = qrCode.GetGraphic(pixelsPerModule, Color.Black, Color.White, true);
                
                // 转换为BitmapImage
                return ConvertToBitmapImage(qrCodeImage);
            }
            catch (Exception ex)
            {
                throw new Exception($"生成二维码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成带Logo的二维码
        /// </summary>
        /// <param name="text">要编码的文本</param>
        /// <param name="logoPath">Logo图片路径</param>
        /// <param name="pixelsPerModule">每个模块的像素数</param>
        /// <returns>二维码BitmapImage</returns>
        public static BitmapImage GenerateQRCodeWithLogo(string text, string logoPath, int pixelsPerModule = 10)
        {
            try
            {
                using var qrGenerator = new QRCodeGenerator();
                using var qrCodeData = qrGenerator.CreateQrCode(text, QRCodeGenerator.ECCLevel.M);
                using var qrCode = new QRCode(qrCodeData);
                
                Bitmap logo = null;
                if (File.Exists(logoPath))
                {
                    logo = new Bitmap(logoPath);
                }
                
                // 生成二维码位图
                using var qrCodeImage = qrCode.GetGraphic(pixelsPerModule, Color.Black, Color.White, logo);
                
                return ConvertToBitmapImage(qrCodeImage);
            }
            catch (Exception ex)
            {
                throw new Exception($"生成带Logo的二维码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成极客风格的二维码
        /// </summary>
        /// <param name="text">要编码的文本</param>
        /// <param name="pixelsPerModule">每个模块的像素数</param>
        /// <returns>二维码BitmapImage</returns>
        public static BitmapImage GenerateGeekStyleQRCode(string text, int pixelsPerModule = 12)
        {
            try
            {
                using var qrGenerator = new QRCodeGenerator();
                using var qrCodeData = qrGenerator.CreateQrCode(text, QRCodeGenerator.ECCLevel.M);
                using var qrCode = new QRCode(qrCodeData);
                
                // 极客风格配色：青色前景，深色背景
                var foregroundColor = Color.FromArgb(0, 216, 255); // #00D8FF
                var backgroundColor = Color.FromArgb(13, 17, 23);   // #0D1117
                
                using var qrCodeImage = qrCode.GetGraphic(pixelsPerModule, foregroundColor, backgroundColor, true);
                
                // 添加边框和阴影效果
                var enhancedImage = AddBorderAndShadow(qrCodeImage);
                
                return ConvertToBitmapImage(enhancedImage);
            }
            catch (Exception ex)
            {
                throw new Exception($"生成极客风格二维码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加边框和阴影效果
        /// </summary>
        private static Bitmap AddBorderAndShadow(Bitmap originalImage)
        {
            int borderSize = 20;
            int shadowOffset = 8;
            
            var newWidth = originalImage.Width + borderSize * 2 + shadowOffset;
            var newHeight = originalImage.Height + borderSize * 2 + shadowOffset;
            
            var enhancedImage = new Bitmap(newWidth, newHeight);
            
            using (var graphics = Graphics.FromImage(enhancedImage))
            {
                graphics.Clear(Color.Transparent);
                
                // 绘制阴影
                using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    graphics.FillRoundedRectangle(shadowBrush, 
                        shadowOffset, shadowOffset, 
                        originalImage.Width + borderSize * 2, 
                        originalImage.Height + borderSize * 2, 
                        10);
                }
                
                // 绘制背景
                using (var backgroundBrush = new SolidBrush(Color.FromArgb(33, 38, 45))) // #21262D
                {
                    graphics.FillRoundedRectangle(backgroundBrush, 
                        0, 0, 
                        originalImage.Width + borderSize * 2, 
                        originalImage.Height + borderSize * 2, 
                        10);
                }
                
                // 绘制边框
                using (var borderPen = new Pen(Color.FromArgb(48, 54, 61), 2)) // #30363D
                {
                    graphics.DrawRoundedRectangle(borderPen, 
                        1, 1, 
                        originalImage.Width + borderSize * 2 - 2, 
                        originalImage.Height + borderSize * 2 - 2, 
                        10);
                }
                
                // 绘制二维码
                graphics.DrawImage(originalImage, borderSize, borderSize);
            }
            
            return enhancedImage;
        }

        /// <summary>
        /// 将Bitmap转换为BitmapImage
        /// </summary>
        private static BitmapImage ConvertToBitmapImage(Bitmap bitmap)
        {
            using var memory = new MemoryStream();
            bitmap.Save(memory, ImageFormat.Png);
            memory.Position = 0;
            
            var bitmapImage = new BitmapImage();
            bitmapImage.BeginInit();
            bitmapImage.StreamSource = memory;
            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
            bitmapImage.EndInit();
            bitmapImage.Freeze();
            
            return bitmapImage;
        }
    }

    /// <summary>
    /// Graphics扩展方法
    /// </summary>
    public static class GraphicsExtensions
    {
        public static void FillRoundedRectangle(this Graphics graphics, Brush brush, float x, float y, float width, float height, float radius)
        {
            using var path = CreateRoundedRectanglePath(x, y, width, height, radius);
            graphics.FillPath(brush, path);
        }

        public static void DrawRoundedRectangle(this Graphics graphics, Pen pen, float x, float y, float width, float height, float radius)
        {
            using var path = CreateRoundedRectanglePath(x, y, width, height, radius);
            graphics.DrawPath(pen, path);
        }

        private static System.Drawing.Drawing2D.GraphicsPath CreateRoundedRectanglePath(float x, float y, float width, float height, float radius)
        {
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            
            path.AddArc(x, y, radius * 2, radius * 2, 180, 90);
            path.AddArc(x + width - radius * 2, y, radius * 2, radius * 2, 270, 90);
            path.AddArc(x + width - radius * 2, y + height - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(x, y + height - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();
            
            return path;
        }
    }
}
