using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.IO;

namespace RemoteControl.Desktop
{
    public static class InputSimulator
    {
        private static readonly string LogFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "shortcut_debug.log");

        private static void WriteLog(string message)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";
                File.AppendAllText(LogFilePath, logMessage + Environment.NewLine);
            }
            catch
            {
                // 忽略日志写入错误
            }
        }

        // Windows API 常量
        private const int MOUSEEVENTF_MOVE = 0x0001;
        private const int MOUSEEVENTF_LEFTDOWN = 0x0002;
        private const int MOUSEEVENTF_LEFTUP = 0x0004;
        private const int MOUSEEVENTF_RIGHTDOWN = 0x0008;
        private const int MOUSEEVENTF_RIGHTUP = 0x0010;
        private const int MOUSEEVENTF_WHEEL = 0x0800;

        private static bool _isDragging = false;

        // Windows API 导入
        [DllImport("user32.dll")]
        private static extern void mouse_event(int dwFlags, int dx, int dy, int dwData, int dwExtraInfo);

        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [DllImport("user32.dll")]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, int dwExtraInfo);

        [DllImport("user32.dll")]
        private static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

        [StructLayout(LayoutKind.Sequential)]
        public struct INPUT
        {
            public uint type;
            public INPUTUNION union;
        }

        [StructLayout(LayoutKind.Explicit)]
        public struct INPUTUNION
        {
            [FieldOffset(0)]
            public MOUSEINPUT mi;
            [FieldOffset(0)]
            public KEYBDINPUT ki;
            [FieldOffset(0)]
            public HARDWAREINPUT hi;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MOUSEINPUT
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct HARDWAREINPUT
        {
            public uint uMsg;
            public ushort wParamL;
            public ushort wParamH;
        }

        private const uint INPUT_KEYBOARD = 1;
        private const uint KEYEVENTF_KEYDOWN = 0x0000;
        private const uint KEYEVENTF_KEYUP = 0x0002;

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }



        /// <summary>
        /// 移动鼠标
        /// </summary>
        public static void MoveMouse(int deltaX, int deltaY)
        {
            try
            {
                GetCursorPos(out POINT currentPos);
                int newX = currentPos.X + deltaX;
                int newY = currentPos.Y + deltaY;

                // 确保坐标在屏幕范围内
                newX = Math.Max(0, Math.Min(Screen.PrimaryScreen.Bounds.Width - 1, newX));
                newY = Math.Max(0, Math.Min(Screen.PrimaryScreen.Bounds.Height - 1, newY));

                SetCursorPos(newX, newY);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移动鼠标失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 左键点击
        /// </summary>
        public static void LeftClick()
        {
            try
            {
                mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
                System.Threading.Thread.Sleep(10);
                mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"左键点击失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 右键点击
        /// </summary>
        public static void RightClick()
        {
            try
            {
                mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0);
                System.Threading.Thread.Sleep(10);
                mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"右键点击失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 滚轮滚动
        /// </summary>
        public static void Scroll(int delta)
        {
            try
            {
                // delta 正值向上滚动，负值向下滚动
                mouse_event(MOUSEEVENTF_WHEEL, 0, 0, delta * 120, 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"滚轮滚动失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送按键
        /// </summary>
        public static void SendKey(string key)
        {
            try
            {
                if (string.IsNullOrEmpty(key)) return;

                // 处理特殊按键
                switch (key.ToLower())
                {
                    case "backspace":
                        SendKeyCode(Keys.Back);
                        break;
                    case "enter":
                        SendKeyCode(Keys.Enter);
                        break;
                    case "tab":
                        SendKeyCode(Keys.Tab);
                        break;
                    case "escape":
                        SendKeyCode(Keys.Escape);
                        break;
                    case "space":
                    case " ":
                        SendKeyCode(Keys.Space);
                        break;
                    case "delete":
                        SendKeyCode(Keys.Delete);
                        break;
                    case "home":
                        SendKeyCode(Keys.Home);
                        break;
                    case "end":
                        SendKeyCode(Keys.End);
                        break;
                    case "pageup":
                        SendKeyCode(Keys.PageUp);
                        break;
                    case "pagedown":
                        SendKeyCode(Keys.PageDown);
                        break;
                    case "arrowup":
                        SendKeyCode(Keys.Up);
                        break;
                    case "arrowdown":
                        SendKeyCode(Keys.Down);
                        break;
                    case "arrowleft":
                        SendKeyCode(Keys.Left);
                        break;
                    case "arrowright":
                        SendKeyCode(Keys.Right);
                        break;
                    default:
                        // 处理普通字符
                        if (key.Length == 1)
                        {
                            char c = key[0];
                            if (char.IsLetter(c))
                            {
                                // 字母
                                Keys keyCode = (Keys)Enum.Parse(typeof(Keys), c.ToString().ToUpper());
                                SendKeyCode(keyCode);
                            }
                            else if (char.IsDigit(c))
                            {
                                // 数字
                                Keys keyCode = (Keys)Enum.Parse(typeof(Keys), "D" + c);
                                SendKeyCode(keyCode);
                            }
                            else
                            {
                                // 其他字符，使用SendKeys
                                SendKeys.SendWait(key);
                            }
                        }
                        else
                        {
                            // 多字符字符串，使用SendKeys
                            SendKeys.SendWait(key);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送按键失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送按键代码
        /// </summary>
        private static void SendKeyCode(Keys key)
        {
            try
            {
                byte vkCode = (byte)key;
                keybd_event(vkCode, 0, 0, 0); // 按下
                System.Threading.Thread.Sleep(10);
                keybd_event(vkCode, 0, KEYEVENTF_KEYUP, 0); // 释放
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送按键代码失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送文本（直接输入，不触发输入法）
        /// </summary>
        public static void SendText(string text)
        {
            try
            {
                if (string.IsNullOrEmpty(text)) return;

                // 使用SendKeys直接发送文本
                SendKeys.SendWait(text);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送文本失败: {ex.Message}");
                // 如果失败，回退到逐字符发送
                foreach (char c in text)
                {
                    SendKey(c.ToString());
                }
            }
        }

        /// <summary>
        /// 发送批量文本（优化的文本输入）
        /// </summary>
        public static void SendBulkText(string text)
        {
            try
            {
                if (string.IsNullOrEmpty(text)) return;

                // 对特殊字符进行转义处理
                string escapedText = EscapeSpecialChars(text);

                // 使用SendKeys发送
                SendKeys.SendWait(escapedText);
                Console.WriteLine($"批量发送文本: {text.Length} 字符");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量发送文本失败: {ex.Message}");
                // 回退到逐字符发送
                foreach (char c in text)
                {
                    SendText(c.ToString());
                    System.Threading.Thread.Sleep(1); // 短暂延迟避免丢失
                }
            }
        }

        /// <summary>
        /// 处理文本差异操作
        /// </summary>
        public static void ProcessTextDiff(dynamic operations)
        {
            try
            {
                foreach (var operation in operations)
                {
                    string opType = operation.type;

                    switch (opType)
                    {
                        case "add":
                            string textToAdd = operation.text;
                            SendBulkText(textToAdd);
                            break;

                        case "delete":
                            int deleteCount = operation.count;
                            for (int i = 0; i < deleteCount; i++)
                            {
                                SendKey("Backspace");
                                System.Threading.Thread.Sleep(5); // 短暂延迟确保删除完成
                            }
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理文本差异失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 转义SendKeys特殊字符
        /// </summary>
        private static string EscapeSpecialChars(string text)
        {
            // SendKeys特殊字符需要转义
            return text.Replace("+", "{+}")
                      .Replace("^", "{^}")
                      .Replace("%", "{%}")
                      .Replace("~", "{~}")
                      .Replace("(", "{(}")
                      .Replace(")", "{)}")
                      .Replace("{", "{{}")
                      .Replace("}", "{}}")
                      .Replace("[", "{[}")
                      .Replace("]", "{]}");
        }

        /// <summary>
        /// 发送快捷键组合 - 使用SendInput API
        /// </summary>
        public static void SendShortcut(string shortcut)
        {
            try
            {
                if (string.IsNullOrEmpty(shortcut)) return;

                WriteLog($"处理快捷键: '{shortcut}'");

                // 解析快捷键字符串，例如 "ctrl+c", "alt+tab", "ctrl+shift+t"
                var keys = shortcut.ToLower().Split('+');
                var modifiers = new List<Keys>();
                Keys? mainKey = null;

                WriteLog($"分解后的按键: [{string.Join(", ", keys)}]");

                foreach (var key in keys)
                {
                    switch (key.Trim())
                    {
                        case "ctrl":
                        case "control":
                            modifiers.Add(Keys.ControlKey);
                            break;
                        case "alt":
                            modifiers.Add(Keys.Menu);
                            break;
                        case "shift":
                            modifiers.Add(Keys.ShiftKey);
                            break;
                        case "win":
                        case "windows":
                            modifiers.Add(Keys.LWin);
                            break;
                        default:
                            // 主按键
                            mainKey = ParseKey(key.Trim());
                            break;
                    }
                }

                if (mainKey.HasValue)
                {
                    WriteLog($"发送快捷键组合: 修饰键数量={modifiers.Count}, 主键={mainKey.Value}");

                    // 创建输入数组
                    var inputs = new List<INPUT>();

                    // 按下修饰键
                    foreach (var modifier in modifiers)
                    {
                        WriteLog($"按下修饰键: {modifier} (键码={(ushort)modifier})");
                        inputs.Add(CreateKeyInput((ushort)modifier, KEYEVENTF_KEYDOWN));
                    }

                    // 按下主键
                    WriteLog($"按下主键: {mainKey.Value} (键码={(ushort)mainKey.Value})");
                    inputs.Add(CreateKeyInput((ushort)mainKey.Value, KEYEVENTF_KEYDOWN));

                    // 释放主键
                    WriteLog($"释放主键: {mainKey.Value} (键码={(ushort)mainKey.Value})");
                    inputs.Add(CreateKeyInput((ushort)mainKey.Value, KEYEVENTF_KEYUP));

                    // 释放修饰键（逆序）
                    for (int i = modifiers.Count - 1; i >= 0; i--)
                    {
                        WriteLog($"释放修饰键: {modifiers[i]} (键码={(ushort)modifiers[i]})");
                        inputs.Add(CreateKeyInput((ushort)modifiers[i], KEYEVENTF_KEYUP));
                    }

                    // 发送所有输入
                    WriteLog($"发送 {inputs.Count} 个输入事件");
                    uint result = SendInput((uint)inputs.Count, inputs.ToArray(), Marshal.SizeOf(typeof(INPUT)));
                    WriteLog($"SendInput 返回值: {result}");

                    WriteLog("快捷键发送完成");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"发送快捷键失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建键盘输入结构
        /// </summary>
        private static INPUT CreateKeyInput(ushort keyCode, uint flags)
        {
            return new INPUT
            {
                type = INPUT_KEYBOARD,
                union = new INPUTUNION
                {
                    ki = new KEYBDINPUT
                    {
                        wVk = keyCode,
                        wScan = 0,
                        dwFlags = flags,
                        time = 0,
                        dwExtraInfo = IntPtr.Zero
                    }
                }
            };
        }

        /// <summary>
        /// 解析按键字符串为Keys枚举
        /// </summary>
        private static Keys ParseKey(string key)
        {
            switch (key.ToLower())
            {
                case "a": return Keys.A;
                case "b": return Keys.B;
                case "c": return Keys.C;
                case "d": return Keys.D;
                case "e": return Keys.E;
                case "f": return Keys.F;
                case "g": return Keys.G;
                case "h": return Keys.H;
                case "i": return Keys.I;
                case "j": return Keys.J;
                case "k": return Keys.K;
                case "l": return Keys.L;
                case "m": return Keys.M;
                case "n": return Keys.N;
                case "o": return Keys.O;
                case "p": return Keys.P;
                case "q": return Keys.Q;
                case "r": return Keys.R;
                case "s": return Keys.S;
                case "t": return Keys.T;
                case "u": return Keys.U;
                case "v": return Keys.V;
                case "w": return Keys.W;
                case "x": return Keys.X;
                case "y": return Keys.Y;
                case "z": return Keys.Z;
                case "0": return Keys.D0;
                case "1": return Keys.D1;
                case "2": return Keys.D2;
                case "3": return Keys.D3;
                case "4": return Keys.D4;
                case "5": return Keys.D5;
                case "6": return Keys.D6;
                case "7": return Keys.D7;
                case "8": return Keys.D8;
                case "9": return Keys.D9;
                case "f1": return Keys.F1;
                case "f2": return Keys.F2;
                case "f3": return Keys.F3;
                case "f4": return Keys.F4;
                case "f5": return Keys.F5;
                case "f6": return Keys.F6;
                case "f7": return Keys.F7;
                case "f8": return Keys.F8;
                case "f9": return Keys.F9;
                case "f10": return Keys.F10;
                case "f11": return Keys.F11;
                case "f12": return Keys.F12;
                case "tab": return Keys.Tab;
                case "enter": return Keys.Enter;
                case "space": return Keys.Space;
                case "escape": case "esc": return Keys.Escape;
                case "backspace": return Keys.Back;
                case "delete": case "del": return Keys.Delete;
                case "home": return Keys.Home;
                case "end": return Keys.End;
                case "pageup": return Keys.PageUp;
                case "pagedown": return Keys.PageDown;
                case "up": return Keys.Up;
                case "down": return Keys.Down;
                case "left": return Keys.Left;
                case "right": return Keys.Right;
                default:
                    // 如果无法解析，返回空格键作为默认值
                    return Keys.Space;
            }
        }

        /// <summary>
        /// 开始拖拽（按下左键）
        /// </summary>
        public static void StartDrag()
        {
            try
            {
                if (!_isDragging)
                {
                    mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
                    _isDragging = true;
                    Console.WriteLine("开始拖拽");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"开始拖拽失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 拖拽移动
        /// </summary>
        public static void DragMove(int deltaX, int deltaY)
        {
            try
            {
                if (_isDragging)
                {
                    // 在拖拽状态下移动鼠标
                    MoveMouse(deltaX, deltaY);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"拖拽移动失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 结束拖拽（释放左键）
        /// </summary>
        public static void EndDrag()
        {
            try
            {
                if (_isDragging)
                {
                    mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
                    _isDragging = false;
                    Console.WriteLine("结束拖拽");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"结束拖拽失败: {ex.Message}");
            }
        }
    }
}
