using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using WebSocketSharp;
using WebSocketSharp.Server;
using Newtonsoft.Json;
using KeywordByAny.Desktop.Models;

namespace KeywordByAny.Desktop.Services
{
    /// <summary>
    /// WebSocket服务器
    /// </summary>
    public class WebSocketServer
    {
        private WebSocketSharp.Server.WebSocketServer _server;
        private HttpListener _httpListener;
        private readonly InputSimulator _inputSimulator;
        private readonly AppSettings _settings;
        private readonly string _webRoot;
        private volatile bool _isRunning = false;
        private readonly object _lockObject = new object();

        public event Action<string> OnLog;
        public event Action<string> OnClientConnected;
        public event Action<string> OnClientDisconnected;

        public bool IsRunning => _isRunning;

        public WebSocketServer(InputSimulator inputSimulator, AppSettings settings)
        {
            _inputSimulator = inputSimulator;
            _settings = settings;
            _webRoot = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "web");
        }

        /// <summary>
        /// 启动服务器
        /// </summary>
        public async Task StartAsync()
        {
            lock (_lockObject)
            {
                if (_isRunning)
                {
                    OnLog?.Invoke("服务器已在运行中");
                    return;
                }
            }

            try
            {
                // 检查端口是否可用
                int availablePort = FindAvailablePort(_settings.Port);
                if (availablePort != _settings.Port)
                {
                    OnLog?.Invoke($"端口 {_settings.Port} 被占用，自动切换到端口 {availablePort}");
                    _settings.Port = availablePort;
                }

                // 启动WebSocket服务器
                _server = new WebSocketSharp.Server.WebSocketServer($"ws://0.0.0.0:{_settings.Port}");
                _server.AddWebSocketService("/input", () => new InputHandler(_inputSimulator, this));
                _server.Start();

                // 启动HTTP服务器提供Web文件
                await StartHttpServerAsync();

                lock (_lockObject)
                {
                    _isRunning = true;
                }

                OnLog?.Invoke($"服务器已启动，端口: {_settings.Port}");
                OnLog?.Invoke($"请在手机浏览器中访问: http://{GetLocalIPAddress()}:{_settings.Port}");
            }
            catch (Exception ex)
            {
                lock (_lockObject)
                {
                    _isRunning = false;
                }
                OnLog?.Invoke($"启动服务器失败: {ex.Message}");

                // 如果是端口占用错误，提供解决建议
                if (ex.Message.Contains("被占用") || ex.Message.Contains("access") || ex.Message.Contains("使用"))
                {
                    OnLog?.Invoke("解决方案：");
                    OnLog?.Invoke("1. 更换端口号（推荐8080-8090之间）");
                    OnLog?.Invoke("2. 关闭占用端口的其他程序");
                    OnLog?.Invoke("3. 以管理员身份运行此程序");
                }

                throw;
            }
        }

        /// <summary>
        /// 停止服务器
        /// </summary>
        public void Stop()
        {
            lock (_lockObject)
            {
                if (!_isRunning)
                {
                    OnLog?.Invoke("服务器未在运行");
                    return;
                }
                _isRunning = false;
            }

            try
            {
                // 停止WebSocket服务器
                if (_server != null)
                {
                    _server.Stop();
                    _server = null;
                }

                // 停止HTTP服务器
                if (_httpListener != null)
                {
                    try
                    {
                        if (_httpListener.IsListening)
                        {
                            _httpListener.Stop();
                        }
                    }
                    catch (ObjectDisposedException)
                    {
                        // HttpListener已被释放，忽略此异常
                    }
                    finally
                    {
                        _httpListener.Close();
                        _httpListener = null;
                    }
                }

                OnLog?.Invoke("服务器已停止");
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"停止服务器时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动HTTP服务器
        /// </summary>
        private async Task StartHttpServerAsync()
        {
            try
            {
                // 清理之前的HttpListener
                if (_httpListener != null)
                {
                    try
                    {
                        if (_httpListener.IsListening)
                        {
                            _httpListener.Stop();
                        }
                    }
                    catch (ObjectDisposedException)
                    {
                        // 已被释放，忽略
                    }
                    finally
                    {
                        _httpListener.Close();
                        _httpListener = null;
                    }
                }

                _httpListener = new HttpListener();
                _httpListener.Prefixes.Add($"http://+:{_settings.Port}/");
                _httpListener.Start();

                _ = Task.Run(async () =>
                {
                    while (true)
                    {
                        try
                        {
                            // 检查是否还在运行
                            lock (_lockObject)
                            {
                                if (!_isRunning || _httpListener == null)
                                    break;
                            }

                            var context = await _httpListener.GetContextAsync();
                            _ = Task.Run(() => HandleHttpRequest(context));
                        }
                        catch (ObjectDisposedException)
                        {
                            // HttpListener已被释放，正常退出
                            break;
                        }
                        catch (HttpListenerException ex) when (ex.ErrorCode == 995 || ex.ErrorCode == 6)
                        {
                            // 操作被取消或句柄无效，正常退出
                            break;
                        }
                        catch (Exception ex)
                        {
                            lock (_lockObject)
                            {
                                if (_isRunning && _httpListener != null)
                                {
                                    OnLog?.Invoke($"HTTP请求处理错误: {ex.Message}");
                                }
                            }
                            break;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"启动HTTP服务器失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理HTTP请求
        /// </summary>
        private void HandleHttpRequest(HttpListenerContext context)
        {
            try
            {
                string path = context.Request.Url.AbsolutePath;
                if (path == "/") path = "/index.html";

                string filePath = Path.Combine(_webRoot, path.TrimStart('/'));

                if (File.Exists(filePath))
                {
                    byte[] content = ReadFileWithRetry(filePath);
                    if (content != null)
                    {
                        string contentType = GetContentType(Path.GetExtension(filePath));
                        context.Response.ContentType = contentType;
                        context.Response.ContentLength64 = content.Length;
                        context.Response.OutputStream.Write(content, 0, content.Length);
                    }
                    else
                    {
                        context.Response.StatusCode = 500;
                        byte[] errorContent = Encoding.UTF8.GetBytes("File is temporarily unavailable");
                        context.Response.OutputStream.Write(errorContent, 0, errorContent.Length);
                    }
                }
                else
                {
                    context.Response.StatusCode = 404;
                    byte[] notFound = Encoding.UTF8.GetBytes("File not found");
                    context.Response.OutputStream.Write(notFound, 0, notFound.Length);
                }

                context.Response.Close();
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"处理HTTP请求错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取内容类型
        /// </summary>
        private string GetContentType(string extension)
        {
            return extension.ToLower() switch
            {
                ".html" => "text/html; charset=utf-8",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".json" => "application/json",
                ".png" => "image/png",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".gif" => "image/gif",
                ".ico" => "image/x-icon",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// 检查端口是否可用
        /// </summary>
        private bool IsPortAvailable(int port)
        {
            try
            {
                var ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();
                var tcpConnInfoArray = ipGlobalProperties.GetActiveTcpListeners();

                return !tcpConnInfoArray.Any(endpoint => endpoint.Port == port);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 查找可用端口
        /// </summary>
        private int FindAvailablePort(int startPort)
        {
            // 首先检查原始端口
            if (IsPortAvailable(startPort))
                return startPort;

            // 在常用端口范围内查找可用端口
            int[] commonPorts = { 8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089, 8090, 3000, 3001, 5000, 5001 };

            foreach (int port in commonPorts)
            {
                if (port != startPort && IsPortAvailable(port))
                {
                    return port;
                }
            }

            // 如果常用端口都被占用，在8000-9000范围内查找
            for (int port = 8000; port <= 9000; port++)
            {
                if (IsPortAvailable(port))
                {
                    return port;
                }
            }

            // 如果还是找不到，返回原始端口（让系统报错）
            return startPort;
        }

        /// <summary>
        /// 重试读取文件
        /// </summary>
        private byte[] ReadFileWithRetry(string filePath, int maxRetries = 3)
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    // 使用FileShare.ReadWrite允许其他进程同时访问文件
                    using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    using var memoryStream = new MemoryStream();
                    fileStream.CopyTo(memoryStream);
                    return memoryStream.ToArray();
                }
                catch (IOException) when (i < maxRetries - 1)
                {
                    // 文件被占用，等待一小段时间后重试
                    System.Threading.Thread.Sleep(50);
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"读取文件失败: {filePath}, 错误: {ex.Message}");
                    break;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取本机IP地址
        /// </summary>
        private string GetLocalIPAddress()
        {
            try
            {
                using var socket = new System.Net.Sockets.Socket(System.Net.Sockets.AddressFamily.InterNetwork,
                    System.Net.Sockets.SocketType.Dgram, 0);
                socket.Connect("*******", 65530);
                return ((IPEndPoint)socket.LocalEndPoint).Address.ToString();
            }
            catch
            {
                return "localhost";
            }
        }

        internal void NotifyClientConnected(string clientId)
        {
            OnClientConnected?.Invoke(clientId);
        }

        internal void NotifyClientDisconnected(string clientId)
        {
            OnClientDisconnected?.Invoke(clientId);
        }
    }

    /// <summary>
    /// WebSocket输入处理器
    /// </summary>
    public class InputHandler : WebSocketBehavior
    {
        private readonly InputSimulator _inputSimulator;
        private readonly WebSocketServer _server;
        private readonly ShortcutHandler _shortcutHandler;

        public InputHandler(InputSimulator inputSimulator, WebSocketServer server)
        {
            _inputSimulator = inputSimulator;
            _server = server;
            _shortcutHandler = new ShortcutHandler(inputSimulator);
        }

        protected override void OnOpen()
        {
            _server.NotifyClientConnected(ID);
        }

        protected override void OnClose(CloseEventArgs e)
        {
            _server.NotifyClientDisconnected(ID);
        }

        protected override void OnMessage(MessageEventArgs e)
        {
            try
            {
                var inputEvent = JsonConvert.DeserializeObject<InputEvent>(e.Data);
                ProcessInputEvent(inputEvent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理输入事件错误: {ex.Message}");
            }
        }

        private void ProcessInputEvent(InputEvent inputEvent)
        {
            switch (inputEvent.Type)
            {
                case InputEventType.MouseMove:
                    _inputSimulator.MoveMouse(inputEvent.DeltaX, inputEvent.DeltaY);
                    break;

                case InputEventType.MouseClick:
                    _inputSimulator.MouseClick(inputEvent.Button);
                    break;

                case InputEventType.MouseScroll:
                    _inputSimulator.MouseScroll(inputEvent.DeltaY);
                    break;

                case InputEventType.KeyPress:
                    if (!string.IsNullOrEmpty(inputEvent.Text))
                    {
                        _inputSimulator.SendText(inputEvent.Text);
                    }
                    else if (!string.IsNullOrEmpty(inputEvent.Key))
                    {
                        _shortcutHandler.HandleKey(inputEvent.Key, inputEvent.CtrlKey, inputEvent.AltKey, inputEvent.ShiftKey);
                    }
                    break;

                case InputEventType.Shortcut:
                    _shortcutHandler.HandleShortcut(inputEvent.Key);
                    break;
            }
        }
    }
}
