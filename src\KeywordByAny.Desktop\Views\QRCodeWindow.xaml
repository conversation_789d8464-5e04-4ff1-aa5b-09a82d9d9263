<Window x:Class="KeywordByAny.Desktop.Views.QRCodeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="连接二维码" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#0D1117">
    
    <Window.Resources>
        <!-- 极客风格样式 -->
        <SolidColorBrush x:Key="BgPrimary" Color="#0D1117"/>
        <SolidColorBrush x:Key="BgSecondary" Color="#161B22"/>
        <SolidColorBrush x:Key="BgTertiary" Color="#21262D"/>
        <SolidColorBrush x:Key="TextPrimary" Color="#F0F6FC"/>
        <SolidColorBrush x:Key="TextSecondary" Color="#8B949E"/>
        <SolidColorBrush x:Key="AccentPrimary" Color="#00D8FF"/>
        <SolidColorBrush x:Key="AccentSecondary" Color="#7C3AED"/>
        <SolidColorBrush x:Key="BorderPrimary" Color="#30363D"/>
        
        <Style x:Key="GeekButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource BgTertiary}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderPrimary}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="Margin" Value="6"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource AccentPrimary}"/>
                                <Setter Property="Foreground" Value="{StaticResource BgPrimary}"/>
                                <Setter Property="BorderBrush" Value="{StaticResource AccentPrimary}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource AccentSecondary}"/>
                                <Setter Property="BorderBrush" Value="{StaticResource AccentSecondary}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <Border Grid.Row="0" Background="{StaticResource BgSecondary}" Padding="20,16">
            <StackPanel>
                <TextBlock Text="扫描二维码连接" 
                           FontSize="20" 
                           FontWeight="Bold"
                           Foreground="{StaticResource TextPrimary}"
                           FontFamily="Consolas, 'Courier New', monospace"
                           HorizontalAlignment="Center"/>
                <TextBlock Text="使用手机扫描下方二维码即可连接" 
                           FontSize="12" 
                           Foreground="{StaticResource TextSecondary}"
                           FontFamily="Consolas, 'Courier New', monospace"
                           HorizontalAlignment="Center"
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- 二维码显示区域 -->
        <Border Grid.Row="1" 
                Background="{StaticResource BgSecondary}" 
                Margin="20"
                CornerRadius="12"
                BorderBrush="{StaticResource BorderPrimary}"
                BorderThickness="1">
            <Grid>
                <Image x:Name="QRCodeImage" 
                       Stretch="Uniform" 
                       Margin="20"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
                
                <!-- 加载提示 -->
                <StackPanel x:Name="LoadingPanel" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center">
                    <TextBlock Text="正在生成二维码..." 
                               FontSize="14" 
                               Foreground="{StaticResource TextSecondary}"
                               FontFamily="Consolas, 'Courier New', monospace"
                               HorizontalAlignment="Center"/>
                    <ProgressBar IsIndeterminate="True" 
                                Width="200" 
                                Height="4" 
                                Margin="0,10,0,0"
                                Background="{StaticResource BgTertiary}"
                                Foreground="{StaticResource AccentPrimary}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 连接信息 -->
        <Border Grid.Row="2" 
                Background="{StaticResource BgTertiary}" 
                Margin="20,0"
                CornerRadius="8"
                BorderBrush="{StaticResource BorderPrimary}"
                BorderThickness="1"
                Padding="16">
            <StackPanel>
                <TextBlock Text="连接信息" 
                           FontSize="14" 
                           FontWeight="Bold"
                           Foreground="{StaticResource TextPrimary}"
                           FontFamily="Consolas, 'Courier New', monospace"
                           Margin="0,0,0,8"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" 
                               Text="服务器地址: " 
                               FontSize="12" 
                               Foreground="{StaticResource TextSecondary}"
                               FontFamily="Consolas, 'Courier New', monospace"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" 
                               x:Name="ServerAddressText"
                               Text="http://*************:8080" 
                               FontSize="12" 
                               Foreground="{StaticResource AccentPrimary}"
                               FontFamily="Consolas, 'Courier New', monospace"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" 
                               Text="配对码: " 
                               FontSize="12" 
                               Foreground="{StaticResource TextSecondary}"
                               FontFamily="Consolas, 'Courier New', monospace"
                               Margin="0,4,0,0"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" 
                               x:Name="PairingCodeText"
                               Text="123456" 
                               FontSize="14" 
                               FontWeight="Bold"
                               Foreground="{StaticResource AccentPrimary}"
                               FontFamily="Consolas, 'Courier New', monospace"
                               Margin="0,4,0,0"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" 
                               Text="有效期: " 
                               FontSize="12" 
                               Foreground="{StaticResource TextSecondary}"
                               FontFamily="Consolas, 'Courier New', monospace"
                               Margin="0,4,0,0"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" 
                               x:Name="ExpirationText"
                               Text="9分58秒" 
                               FontSize="12" 
                               Foreground="{StaticResource TextPrimary}"
                               FontFamily="Consolas, 'Courier New', monospace"
                               Margin="0,4,0,0"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="20">
            <Button Content="刷新二维码" 
                    x:Name="RefreshButton"
                    Style="{StaticResource GeekButtonStyle}"
                    Click="RefreshButton_Click"/>
            <Button Content="复制地址" 
                    x:Name="CopyButton"
                    Style="{StaticResource GeekButtonStyle}"
                    Click="CopyButton_Click"/>
            <Button Content="关闭" 
                    x:Name="CloseButton"
                    Style="{StaticResource GeekButtonStyle}"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
