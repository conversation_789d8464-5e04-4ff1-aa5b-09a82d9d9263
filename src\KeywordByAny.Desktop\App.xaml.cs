using System.Windows;

namespace KeywordByAny.Desktop
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // 设置应用程序异常处理
            DispatcherUnhandledException += (s, ex) =>
            {
                MessageBox.Show($"应用程序发生错误: {ex.Exception.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                ex.Handled = true;
            };
        }
    }
}
