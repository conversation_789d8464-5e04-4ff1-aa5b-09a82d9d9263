<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Remote Control - 极客触控板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            background: #0d1117;
            color: #f0f6fc;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 8px;
        }

        .header {
            text-align: center;
            padding: 12px 16px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            margin-bottom: 8px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d8ff, transparent);
        }

        .header h2 {
            font-size: 14px;
            font-weight: 600;
            color: #f0f6fc;
            margin: 0;
            letter-spacing: 1px;
        }

        .status {
            font-size: 10px;
            margin-top: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'SF Mono', monospace;
            color: #8b949e;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 6px;
            background: #da3633;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #238636;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }

            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .touchpad {
            flex: 1;
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            margin-bottom: 8px;
            position: relative;
            overflow: hidden;
        }

        .touchpad::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 216, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .touchpad-area {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6e7681;
            font-size: 12px;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-bottom: 8px;
        }

        .btn {
            height: 48px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            color: #f0f6fc;
            font-size: 12px;
            font-weight: 500;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:active {
            background: #30363d;
            border-color: #00d8ff;
            transform: scale(0.98);
        }

        .btn.active {
            background: #1f6feb;
            border-color: #1f6feb;
            color: #ffffff;
        }

        .keyboard-toggle {
            height: 40px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            color: #f0f6fc;
            font-size: 11px;
            font-family: inherit;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .keyboard {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 12px 12px 0 0;
            padding: 12px;
            display: none;
            max-height: 30vh;
            overflow-y: auto;
        }

        .keyboard.show {
            display: block;
        }

        .key-row {
            display: flex;
            gap: 4px;
            margin-bottom: 6px;
            justify-content: center;
        }

        .key {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 4px;
            color: #f0f6fc;
            padding: 8px;
            min-width: 28px;
            font-size: 12px;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.1s ease;
            text-align: center;
        }

        .key:active {
            background: #1f6feb;
            border-color: #1f6feb;
            transform: scale(0.95);
        }

        .key.space {
            flex: 1;
            min-width: 120px;
        }

        .key.wide {
            min-width: 48px;
        }

        .touch-feedback {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(0, 216, 255, 0.3);
            pointer-events: none;
            transform: translate(-50%, -50%) scale(0);
            animation: touch-ripple 0.3s ease-out;
        }

        @keyframes touch-ripple {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 1;
            }

            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h2>REMOTE CONTROL</h2>
            <div class="status" id="status">
                <span class="status-dot" id="statusDot"></span>
                <span id="statusText">CONNECTING...</span>
            </div>
        </div>

        <div class="touchpad" id="touchpad">
            <div class="touchpad-area">
                TOUCHPAD AREA
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="leftClick">L-CLICK</button>
            <button class="btn" id="rightClick">R-CLICK</button>
            <button class="btn" id="scrollMode">SCROLL</button>
        </div>

        <button class="keyboard-toggle" id="keyboardToggle">SHOW KEYBOARD</button>

        <div class="keyboard" id="keyboard">
            <div class="key-row">
                <button class="key" data-key="Escape">ESC</button>
                <button class="key" data-key="Tab">TAB</button>
                <button class="key" data-key="Backspace">⌫</button>
                <button class="key" data-key="Enter">↵</button>
            </div>
            <div class="key-row">
                <button class="key" data-key="ArrowUp">↑</button>
                <button class="key" data-key="ArrowDown">↓</button>
                <button class="key" data-key="ArrowLeft">←</button>
                <button class="key" data-key="ArrowRight">→</button>
            </div>
            <div class="key-row">
                <button class="key space" data-key=" ">SPACE</button>
                <button class="key" data-key="Delete">DEL</button>
            </div>
            <div class="key-row">
                <button class="key" onclick="showNativeKeyboard()">📱 系统键盘</button>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let lastTouch = {
            x: 0,
            y: 0
        };
        let isScrollMode = false;

        // 连接WebSocket
        function connect() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}`;

            console.log('Connecting to:', wsUrl);
            ws = new WebSocket(wsUrl);

            ws.onopen = function () {
                console.log('WebSocket connected successfully');
                isConnected = true;
                updateStatus('CONNECTED', 'connected');
            };

            ws.onclose = function (event) {
                console.log('WebSocket closed:', event.code, event.reason);
                isConnected = false;
                updateStatus('DISCONNECTED', 'disconnected');
                setTimeout(connect, 2000);
            };

            ws.onerror = function (error) {
                console.error('WebSocket error:', error);
                isConnected = false;
                updateStatus('ERROR', 'disconnected');
            };
        }

        function updateStatus(text, className) {
            const statusText = document.getElementById('statusText');
            const statusDot = document.getElementById('statusDot');
            statusText.textContent = text;
            statusDot.className = `status-dot ${className}`;
        }

        function sendMessage(data) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(data));
            }
        }

        // 触控板事件处理
        const touchpad = document.getElementById('touchpad');

        touchpad.addEventListener('touchstart', function (e) {
            e.preventDefault();
            const touch = e.touches[0];
            lastTouch.x = touch.clientX;
            lastTouch.y = touch.clientY;
        });

        touchpad.addEventListener('touchmove', function (e) {
            e.preventDefault();
            const touch = e.touches[0];
            const deltaX = touch.clientX - lastTouch.x;
            const deltaY = touch.clientY - lastTouch.y;

            sendMessage({
                type: 'move',
                deltaX: deltaX * 1.5,
                deltaY: deltaY * 1.5
            });

            lastTouch.x = touch.clientX;
            lastTouch.y = touch.clientY;
        });

        touchpad.addEventListener('click', function () {
            sendMessage({
                type: 'click',
                button: 'left'
            });
        });

        // 按钮事件
        document.getElementById('leftClick').addEventListener('click', function () {
            sendMessage({
                type: 'click',
                button: 'left'
            });
        });

        document.getElementById('rightClick').addEventListener('click', function () {
            sendMessage({
                type: 'click',
                button: 'right'
            });
        });

        document.getElementById('scrollMode').addEventListener('click', function () {
            isScrollMode = !isScrollMode;
            this.classList.toggle('active', isScrollMode);
        });

        // 键盘切换
        document.getElementById('keyboardToggle').addEventListener('click', function () {
            const keyboard = document.getElementById('keyboard');
            const isVisible = keyboard.classList.contains('show');

            if (isVisible) {
                keyboard.classList.remove('show');
                this.textContent = 'SHOW KEYBOARD';
            } else {
                keyboard.classList.add('show');
                this.textContent = 'HIDE KEYBOARD';
            }
        });

        // 系统键盘
        function showNativeKeyboard() {
            const input = document.createElement('input');
            input.type = 'text';
            input.style.position = 'absolute';
            input.style.left = '-9999px';
            document.body.appendChild(input);
            input.focus();

            input.addEventListener('input', function (e) {
                const text = e.target.value;
                if (text) {
                    for (let char of text) {
                        sendMessage({
                            type: 'key',
                            key: char
                        });
                    }
                    e.target.value = '';
                }
            });

            setTimeout(() => {
                if (document.body.contains(input)) {
                    document.body.removeChild(input);
                }
            }, 5000);
        }

        // 键盘按键事件
        document.querySelectorAll('.key').forEach(key => {
            key.addEventListener('click', function () {
                const keyValue = this.getAttribute('data-key');
                sendMessage({
                    type: 'key',
                    key: keyValue
                });
            });
        });

        // 启动连接
        connect();
    </script>
</body>

</html>