/**
 * 快捷键功能管理器
 */
class ShortcutsManager {
    constructor() {
        this.shortcuts = new Map();
        this.recentShortcuts = [];
        this.maxRecentShortcuts = 10;
        
        this.init();
    }

    init() {
        this.loadShortcuts();
        this.loadRecentShortcuts();
    }

    loadShortcuts() {
        // 定义快捷键分类和描述
        this.shortcuts = new Map([
            // 基本编辑
            ['ctrl+c', { name: '复制', category: '基本编辑', description: '复制选中内容' }],
            ['ctrl+v', { name: '粘贴', category: '基本编辑', description: '粘贴剪贴板内容' }],
            ['ctrl+x', { name: '剪切', category: '基本编辑', description: '剪切选中内容' }],
            ['ctrl+z', { name: '撤销', category: '基本编辑', description: '撤销上一步操作' }],
            ['ctrl+y', { name: '重做', category: '基本编辑', description: '重做上一步操作' }],
            ['ctrl+a', { name: '全选', category: '基本编辑', description: '选择全部内容' }],
            ['ctrl+s', { name: '保存', category: '基本编辑', description: '保存当前文档' }],
            ['ctrl+f', { name: '查找', category: '基本编辑', description: '打开查找对话框' }],
            ['ctrl+h', { name: '替换', category: '基本编辑', description: '打开查找替换对话框' }],
            ['ctrl+n', { name: '新建', category: '基本编辑', description: '新建文档' }],
            ['ctrl+o', { name: '打开', category: '基本编辑', description: '打开文档' }],

            // 窗口管理
            ['alt+tab', { name: '切换窗口', category: '窗口管理', description: '在打开的窗口间切换' }],
            ['alt+f4', { name: '关闭窗口', category: '窗口管理', description: '关闭当前窗口' }],
            ['win+d', { name: '显示桌面', category: '窗口管理', description: '显示/隐藏桌面' }],
            ['win+l', { name: '锁定屏幕', category: '窗口管理', description: '锁定计算机' }],
            ['win+r', { name: '运行', category: '窗口管理', description: '打开运行对话框' }],

            // 浏览器
            ['ctrl+t', { name: '新标签页', category: '浏览器', description: '打开新标签页' }],
            ['ctrl+w', { name: '关闭标签页', category: '浏览器', description: '关闭当前标签页' }],
            ['ctrl+shift+t', { name: '恢复标签页', category: '浏览器', description: '恢复最近关闭的标签页' }],
            ['ctrl+r', { name: '刷新', category: '浏览器', description: '刷新当前页面' }],
            ['f5', { name: 'F5刷新', category: '浏览器', description: '刷新当前页面' }],
            ['ctrl+l', { name: '地址栏', category: '浏览器', description: '聚焦到地址栏' }],
            ['f12', { name: '开发者工具', category: '浏览器', description: '打开开发者工具' }],
            ['ctrl+shift+i', { name: '检查元素', category: '浏览器', description: '打开检查元素' }],

            // IDE/编程
            ['ctrl+shift+p', { name: '命令面板', category: 'IDE/编程', description: '打开命令面板' }],
            ['ctrl+p', { name: '快速打开', category: 'IDE/编程', description: '快速打开文件' }],
            ['ctrl+shift+f', { name: '全局搜索', category: 'IDE/编程', description: '在项目中搜索' }],
            ['ctrl+g', { name: '跳转行', category: 'IDE/编程', description: '跳转到指定行' }],
            ['ctrl+d', { name: '选择单词', category: 'IDE/编程', description: '选择当前单词' }],
            ['ctrl+/', { name: '注释', category: 'IDE/编程', description: '切换行注释' }],
            ['ctrl+shift+/', { name: '块注释', category: 'IDE/编程', description: '切换块注释' }],

            // 导航
            ['ctrl+home', { name: '文档开头', category: '导航', description: '跳转到文档开头' }],
            ['ctrl+end', { name: '文档结尾', category: '导航', description: '跳转到文档结尾' }],
            ['ctrl+left', { name: '上一个单词', category: '导航', description: '跳转到上一个单词' }],
            ['ctrl+right', { name: '下一个单词', category: '导航', description: '跳转到下一个单词' }],
            ['home', { name: '行首', category: '导航', description: '跳转到行首' }],
            ['end', { name: '行尾', category: '导航', description: '跳转到行尾' }],
            ['pageup', { name: '上一页', category: '导航', description: '向上翻页' }],
            ['pagedown', { name: '下一页', category: '导航', description: '向下翻页' }],

            // 功能键
            ['f1', { name: 'F1', category: '功能键', description: '帮助' }],
            ['f2', { name: 'F2', category: '功能键', description: '重命名' }],
            ['f3', { name: 'F3', category: '功能键', description: '查找下一个' }],
            ['f4', { name: 'F4', category: '功能键', description: 'F4功能' }],
            ['f6', { name: 'F6', category: '功能键', description: 'F6功能' }],
            ['f7', { name: 'F7', category: '功能键', description: 'F7功能' }],
            ['f8', { name: 'F8', category: '功能键', description: 'F8功能' }],
            ['f9', { name: 'F9', category: '功能键', description: 'F9功能' }],
            ['f10', { name: 'F10', category: '功能键', description: 'F10功能' }],
            ['f11', { name: 'F11', category: '功能键', description: '全屏' }],

            // 特殊按键
            ['escape', { name: 'Esc', category: '特殊按键', description: '取消/退出' }],
            ['enter', { name: '回车', category: '特殊按键', description: '确认/换行' }],
            ['tab', { name: 'Tab', category: '特殊按键', description: '制表符/切换焦点' }],
            ['shift+tab', { name: 'Shift+Tab', category: '特殊按键', description: '反向切换焦点' }],
            ['backspace', { name: '退格', category: '特殊按键', description: '删除前一个字符' }],
            ['delete', { name: '删除', category: '特殊按键', description: '删除后一个字符' }],
            ['space', { name: '空格', category: '特殊按键', description: '空格字符' }],

            // 方向键
            ['up', { name: '↑', category: '方向键', description: '向上' }],
            ['down', { name: '↓', category: '方向键', description: '向下' }],
            ['left', { name: '←', category: '方向键', description: '向左' }],
            ['right', { name: '→', category: '方向键', description: '向右' }]
        ]);
    }

    loadRecentShortcuts() {
        try {
            const saved = localStorage.getItem('keywordByAny_recentShortcuts');
            if (saved) {
                this.recentShortcuts = JSON.parse(saved);
            }
        } catch (e) {
            console.warn('加载最近使用的快捷键失败:', e);
            this.recentShortcuts = [];
        }
    }

    saveRecentShortcuts() {
        try {
            localStorage.setItem('keywordByAny_recentShortcuts', JSON.stringify(this.recentShortcuts));
        } catch (e) {
            console.warn('保存最近使用的快捷键失败:', e);
        }
    }

    sendShortcut(shortcut) {
        if (!wsManager.isConnected) {
            this.showNotConnectedMessage();
            return;
        }

        // 发送快捷键
        const success = wsManager.sendShortcut(shortcut);
        
        if (success) {
            // 添加到最近使用列表
            this.addToRecentShortcuts(shortcut);
            
            // 提供视觉反馈
            this.showShortcutFeedback(shortcut);
            
            // 提供触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }
    }

    addToRecentShortcuts(shortcut) {
        // 移除已存在的项
        const index = this.recentShortcuts.indexOf(shortcut);
        if (index > -1) {
            this.recentShortcuts.splice(index, 1);
        }
        
        // 添加到开头
        this.recentShortcuts.unshift(shortcut);
        
        // 限制数量
        if (this.recentShortcuts.length > this.maxRecentShortcuts) {
            this.recentShortcuts = this.recentShortcuts.slice(0, this.maxRecentShortcuts);
        }
        
        // 保存到本地存储
        this.saveRecentShortcuts();
        
        // 更新UI
        this.updateRecentShortcutsUI();
    }

    updateRecentShortcutsUI() {
        // 如果有最近使用的快捷键区域，更新它
        const recentContainer = document.getElementById('recentShortcuts');
        if (recentContainer && this.recentShortcuts.length > 0) {
            recentContainer.innerHTML = `
                <h3>最近使用</h3>
                <div class="shortcut-buttons">
                    ${this.recentShortcuts.map(shortcut => {
                        const info = this.shortcuts.get(shortcut);
                        const name = info ? info.name : shortcut;
                        return `<button onclick="sendShortcut('${shortcut}')">${name}</button>`;
                    }).join('')}
                </div>
            `;
            recentContainer.style.display = 'block';
        }
    }

    showShortcutFeedback(shortcut) {
        const info = this.shortcuts.get(shortcut);
        const name = info ? info.name : shortcut;
        
        // 创建反馈提示
        const feedback = document.createElement('div');
        feedback.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 122, 204, 0.9);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            z-index: 1000;
            pointer-events: none;
            animation: shortcutFeedback 0.8s ease-out forwards;
        `;
        feedback.textContent = `${name} (${shortcut})`;
        
        // 添加动画样式
        if (!document.getElementById('shortcutFeedbackStyle')) {
            const style = document.createElement('style');
            style.id = 'shortcutFeedbackStyle';
            style.textContent = `
                @keyframes shortcutFeedback {
                    0% {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.8);
                    }
                    20% {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                    80% {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                    100% {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.8);
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(feedback);
        
        // 自动移除
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 800);
    }

    showNotConnectedMessage() {
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            z-index: 1000;
            pointer-events: none;
        `;
        message.textContent = '未连接到服务器';
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 2000);
    }

    getShortcutInfo(shortcut) {
        return this.shortcuts.get(shortcut);
    }

    getShortcutsByCategory(category) {
        const result = [];
        for (const [key, value] of this.shortcuts) {
            if (value.category === category) {
                result.push({ shortcut: key, ...value });
            }
        }
        return result;
    }

    searchShortcuts(query) {
        const results = [];
        const lowerQuery = query.toLowerCase();
        
        for (const [key, value] of this.shortcuts) {
            if (key.includes(lowerQuery) || 
                value.name.toLowerCase().includes(lowerQuery) ||
                value.description.toLowerCase().includes(lowerQuery)) {
                results.push({ shortcut: key, ...value });
            }
        }
        
        return results;
    }
}

// 全局函数
function sendShortcut(shortcut) {
    shortcutsManager.sendShortcut(shortcut);
}

// 创建全局快捷键管理器实例
const shortcutsManager = new ShortcutsManager();
