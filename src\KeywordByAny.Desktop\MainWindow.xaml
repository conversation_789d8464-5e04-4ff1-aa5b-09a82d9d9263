<Window x:Class="KeywordByAny.Desktop.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="KeywordByAny - 无线触控板和键盘"
        Height="650"
        Width="900"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        StateChanged="Window_StateChanged"
        Background="#0D1117">

    <Window.Resources>
        <!-- 极客风格配色 -->
        <SolidColorBrush x:Key="BgPrimary"
                Color="#0D1117"/>
        <SolidColorBrush x:Key="BgSecondary"
                Color="#161B22"/>
        <SolidColorBrush x:Key="BgTertiary"
                Color="#21262D"/>
        <SolidColorBrush x:Key="BgOverlay"
                Color="#30363D"/>

        <SolidColorBrush x:Key="TextPrimary"
                Color="#F0F6FC"/>
        <SolidColorBrush x:Key="TextSecondary"
                Color="#8B949E"/>
        <SolidColorBrush x:Key="TextMuted"
                Color="#6E7681"/>

        <SolidColorBrush x:Key="AccentPrimary"
                Color="#00D8FF"/>
        <SolidColorBrush x:Key="AccentSecondary"
                Color="#7C3AED"/>
        <SolidColorBrush x:Key="AccentSuccess"
                Color="#238636"/>
        <SolidColorBrush x:Key="AccentDanger"
                Color="#DA3633"/>

        <SolidColorBrush x:Key="BorderPrimary"
                Color="#30363D"/>

        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle"
                TargetType="Button">
            <Setter Property="Background"
                    Value="{StaticResource BgOverlay}"/>
            <Setter Property="Foreground"
                    Value="{StaticResource TextPrimary}"/>
            <Setter Property="BorderBrush"
                    Value="{StaticResource BorderPrimary}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Padding"
                    Value="20,12"/>
            <Setter Property="Margin"
                    Value="8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="FontFamily"
                    Value="Segoe UI, Arial"/>
            <Setter Property="FontWeight"
                    Value="Medium"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="{StaticResource AccentPrimary}"/>
                                <Setter Property="Foreground"
                                        Value="{StaticResource BgPrimary}"/>
                                <Setter Property="BorderBrush"
                                        Value="{StaticResource AccentPrimary}"/>
                                <Setter TargetName="border"
                                        Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#00D8FF"
                                                Opacity="0.4"
                                                BlurRadius="12"
                                                ShadowDepth="0"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="{StaticResource AccentSecondary}"/>
                                <Setter Property="BorderBrush"
                                        Value="{StaticResource AccentSecondary}"/>
                                <Setter TargetName="border"
                                        Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#7C3AED"
                                                Opacity="0.6"
                                                BlurRadius="12"
                                                ShadowDepth="0"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 主要按钮样式 -->
        <Style x:Key="PrimaryButtonStyle"
                TargetType="Button"
                BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background"
                    Value="{StaticResource AccentPrimary}"/>
            <Setter Property="Foreground"
                    Value="{StaticResource BgPrimary}"/>
            <Setter Property="BorderBrush"
                    Value="{StaticResource AccentPrimary}"/>
            <Setter Property="FontWeight"
                    Value="Bold"/>
        </Style>

        <!-- 卡片样式 -->
        <Style x:Key="CardStyle"
                TargetType="Border">
            <Setter Property="Background"
                    Value="{StaticResource BgSecondary}"/>
            <Setter Property="BorderBrush"
                    Value="{StaticResource BorderPrimary}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="12"/>
            <Setter Property="Padding"
                    Value="20"/>
            <Setter Property="Margin"
                    Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black"
                            Opacity="0.3"
                            BlurRadius="10"
                            ShadowDepth="2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="TitleStyle"
                TargetType="TextBlock">
            <Setter Property="FontFamily"
                    Value="Segoe UI Light"/>
            <Setter Property="FontSize"
                    Value="24"/>
            <Setter Property="FontWeight"
                    Value="Light"/>
            <Setter Property="Foreground"
                    Value="{StaticResource TextPrimary}"/>
        </Style>

        <!-- 状态指示器样式 -->
        <Style x:Key="StatusIndicatorStyle"
                TargetType="Ellipse">
            <Setter Property="Width"
                    Value="12"/>
            <Setter Property="Height"
                    Value="12"/>
            <Setter Property="Fill"
                    Value="{StaticResource AccentDanger}"/>
            <Setter Property="Margin"
                    Value="0,0,8,0"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 现代化标题栏 -->
        <Border Grid.Row="0"
                Background="{StaticResource BgSecondary}"
                Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="KeywordByAny"
                            Style="{StaticResource TitleStyle}"/>
                    <TextBlock Text="让手机成为你的无线触控板和键盘"
                               FontSize="14"
                               Foreground="{StaticResource TextSecondary}"
                               Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        VerticalAlignment="Center">
                    <Ellipse x:Name="StatusIndicator"
                            Style="{StaticResource StatusIndicatorStyle}"/>
                    <TextBlock x:Name="StatusText"
                            Text="服务器已停止"
                               Foreground="{StaticResource TextSecondary}"
                               FontSize="14"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1"
                Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="3*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧控制面板 -->
            <StackPanel Grid.Column="0">
                <!-- 服务器控制卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="🚀 服务器控制"
                                FontSize="18"
                                FontWeight="SemiBold"
                                   Foreground="{StaticResource TextPrimary}"
                                Margin="0,0,0,15"/>

                        <Button x:Name="StartStopButton"
                                Content="启动服务器"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Click="StartStopButton_Click"
                                HorizontalAlignment="Stretch"/>

                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0"
                                    Text="端口:"
                                       VerticalAlignment="Center"
                                       Foreground="{StaticResource TextSecondary}"/>
                            <TextBox Grid.Column="1"
                                    x:Name="PortTextBox"
                                    Text="8080"
                                     Margin="10,0"
                                     Padding="8"
                                     Background="{StaticResource BgOverlay}"
                                     Foreground="{StaticResource TextPrimary}"
                                     BorderBrush="{StaticResource BorderPrimary}"
                                     BorderThickness="1"
                                     VerticalAlignment="Center"/>
                            <Button Grid.Column="2"
                                    Content="应用"
                                    Click="ApplyPortButton_Click"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Padding="12,8"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 连接信息卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="🌐 连接信息"
                                FontSize="18"
                                FontWeight="SemiBold"
                                   Foreground="{StaticResource TextPrimary}"
                                Margin="0,0,0,15"/>

                        <StackPanel>
                            <TextBlock Text="服务器地址:"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="12"
                                    Margin="0,0,0,5"/>
                            <TextBlock x:Name="IpAddressText"
                                    Text="未启动"
                                       Foreground="{StaticResource AccentPrimary}"
                                       FontFamily="Consolas"
                                       FontSize="14"
                                       Margin="0,0,0,15"/>

                            <TextBlock Text="已连接设备:"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="12"
                                    Margin="0,0,0,5"/>
                            <TextBlock x:Name="ConnectedClientsText"
                                    Text="0"
                                       Foreground="{StaticResource TextPrimary}"
                                       FontSize="16"
                                    FontWeight="Bold"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 快速操作卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="⚡ 快速操作"
                                FontSize="18"
                                FontWeight="SemiBold"
                                   Foreground="{StaticResource TextPrimary}"
                                Margin="0,0,0,15"/>

                        <Button Content="📱 生成二维码"
                                Click="GenerateQRButton_Click"
                                Style="{StaticResource ModernButtonStyle}"
                                HorizontalAlignment="Stretch"
                                Margin="0,0,0,8"/>
                        <Button Content="🌐 打开Web界面"
                                Click="OpenWebButton_Click"
                                Style="{StaticResource ModernButtonStyle}"
                                HorizontalAlignment="Stretch"
                                Margin="0,0,0,8"/>
                        <Button Content="📋 复制地址"
                                Click="CopyAddressButton_Click"
                                Style="{StaticResource ModernButtonStyle}"
                                HorizontalAlignment="Stretch"/>
                    </StackPanel>
                </Border>
            </StackPanel>

            <!-- 右侧日志和设置 -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="2*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 日志卡片 -->
                <Border Grid.Row="0"
                        Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0"
                                Text="📋 系统日志"
                                FontSize="18"
                                FontWeight="SemiBold"
                                   Foreground="{StaticResource TextPrimary}"
                                Margin="0,0,0,15"/>

                        <ScrollViewer Grid.Row="1"
                                      Background="{StaticResource BgPrimary}"
                                      BorderBrush="{StaticResource BorderPrimary}"
                                      BorderThickness="1"
                                      Padding="15">
                            <TextBlock x:Name="LogTextBlock"
                                       TextWrapping="Wrap"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="{StaticResource TextSecondary}"
                                       Text="系统就绪，等待启动服务器..."/>
                        </ScrollViewer>
                    </Grid>
                </Border>

                <!-- 设置卡片 -->
                <Border Grid.Row="1"
                        Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="⚙️ 设置"
                                FontSize="18"
                                FontWeight="SemiBold"
                                   Foreground="{StaticResource TextPrimary}"
                                Margin="0,0,0,15"/>

                        <CheckBox x:Name="StartWithWindowsCheckBox"
                                Content="开机自启动"
                                  Foreground="{StaticResource TextPrimary}"
                                  Margin="0,0,0,8"/>
                        <CheckBox x:Name="MinimizeToTrayCheckBox"
                                Content="最小化到系统托盘"
                                  IsChecked="True"
                                  Foreground="{StaticResource TextPrimary}"
                                  Margin="0,0,0,15"/>

                        <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Right">
                            <Button Content="重置"
                                    Click="ResetSettingsButton_Click"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Padding="15,8"/>
                            <Button Content="保存设置"
                                    Click="SaveSettingsButton_Click"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Padding="15,8"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</Window>
