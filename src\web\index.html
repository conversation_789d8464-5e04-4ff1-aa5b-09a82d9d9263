<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>KeywordByAny - 无线触控板和键盘</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#007ACC">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
</head>

<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>KeywordByAny</h1>
            <p>无线触控板和键盘</p>
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">连接中...</span>
            </div>
        </header>

        <!-- 主菜单 -->
        <main class="main-menu" id="mainMenu">
            <div class="menu-grid">
                <div class="menu-item" onclick="openTouchpad()">
                    <div class="menu-icon">🖱️</div>
                    <h3>触控板</h3>
                    <p>鼠标控制和手势操作</p>
                </div>

                <div class="menu-item" onclick="openKeyboard()">
                    <div class="menu-icon">⌨️</div>
                    <h3>键盘</h3>
                    <p>文字输入和快捷键</p>
                </div>

                <div class="menu-item" onclick="openShortcuts()">
                    <div class="menu-icon">⚡</div>
                    <h3>快捷键</h3>
                    <p>程序员常用快捷键</p>
                </div>

                <div class="menu-item" onclick="openSettings()">
                    <div class="menu-icon">⚙️</div>
                    <h3>设置</h3>
                    <p>灵敏度和偏好设置</p>
                </div>
            </div>
        </main>

        <!-- 触控板界面 -->
        <div class="touchpad-container hidden" id="touchpadContainer">
            <div class="touchpad-header">
                <button class="back-button" onclick="showMainMenu()">← 返回</button>
                <h2>触控板</h2>
                <button class="settings-button" onclick="toggleTouchpadSettings()">⚙️</button>
            </div>

            <div class="touchpad-settings hidden" id="touchpadSettings">
                <div class="setting-item">
                    <label>鼠标灵敏度</label>
                    <input type="range" id="mouseSensitivity" min="0.1" max="3" step="0.1" value="1">
                    <span id="mouseSensitivityValue">1.0</span>
                </div>
                <div class="setting-item">
                    <label>滚轮灵敏度</label>
                    <input type="range" id="scrollSensitivity" min="0.1" max="3" step="0.1" value="1">
                    <span id="scrollSensitivityValue">1.0</span>
                </div>
            </div>

            <div class="touchpad-area" id="touchpadArea">
                <div class="touchpad-hint">
                    <p>单指移动：移动鼠标</p>
                    <p>单击：左键点击</p>
                    <p>双击：双击</p>
                    <p>长按：右键点击</p>
                    <p>双指滚动：滚轮</p>
                </div>
            </div>

            <div class="touchpad-buttons">
                <button class="touchpad-btn left-click" ontouchstart="handleMouseClick('left')"
                    ontouchend="handleMouseRelease()">左键</button>
                <button class="touchpad-btn right-click" ontouchstart="handleMouseClick('right')"
                    ontouchend="handleMouseRelease()">右键</button>
                <button class="touchpad-btn middle-click" ontouchstart="handleMouseClick('middle')"
                    ontouchend="handleMouseRelease()">中键</button>
            </div>
        </div>

        <!-- 键盘界面 -->
        <div class="keyboard-container hidden" id="keyboardContainer">
            <div class="keyboard-header">
                <button class="back-button" onclick="showMainMenu()">← 返回</button>
                <h2>键盘</h2>
                <button class="keyboard-toggle" onclick="toggleVirtualKeyboard()">⌨️</button>
            </div>

            <div class="text-input-area">
                <textarea id="textInput" placeholder="在此输入文字，将实时发送到电脑..." autocomplete="off" autocorrect="off"
                    autocapitalize="off" spellcheck="false"></textarea>
                <div class="input-controls">
                    <button onclick="clearTextInput()">清空</button>
                    <button onclick="sendEnter()">回车</button>
                    <button onclick="sendTab()">Tab</button>
                    <button onclick="sendEscape()">Esc</button>
                </div>
            </div>

            <div class="virtual-keyboard hidden" id="virtualKeyboard">
                <!-- 虚拟键盘将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 快捷键界面 -->
        <div class="shortcuts-container hidden" id="shortcutsContainer">
            <div class="shortcuts-header">
                <button class="back-button" onclick="showMainMenu()">← 返回</button>
                <h2>快捷键</h2>
            </div>

            <div class="shortcuts-grid">
                <!-- 基本编辑 -->
                <div class="shortcut-category">
                    <h3>基本编辑</h3>
                    <div class="shortcut-buttons">
                        <button onclick="sendShortcut('ctrl+c')">复制</button>
                        <button onclick="sendShortcut('ctrl+v')">粘贴</button>
                        <button onclick="sendShortcut('ctrl+x')">剪切</button>
                        <button onclick="sendShortcut('ctrl+z')">撤销</button>
                        <button onclick="sendShortcut('ctrl+y')">重做</button>
                        <button onclick="sendShortcut('ctrl+a')">全选</button>
                        <button onclick="sendShortcut('ctrl+s')">保存</button>
                        <button onclick="sendShortcut('ctrl+f')">查找</button>
                    </div>
                </div>

                <!-- 窗口管理 -->
                <div class="shortcut-category">
                    <h3>窗口管理</h3>
                    <div class="shortcut-buttons">
                        <button onclick="sendShortcut('alt+tab')">切换窗口</button>
                        <button onclick="sendShortcut('alt+f4')">关闭窗口</button>
                        <button onclick="sendShortcut('win+d')">显示桌面</button>
                        <button onclick="sendShortcut('win+l')">锁定屏幕</button>
                    </div>
                </div>

                <!-- 浏览器 -->
                <div class="shortcut-category">
                    <h3>浏览器</h3>
                    <div class="shortcut-buttons">
                        <button onclick="sendShortcut('ctrl+t')">新标签页</button>
                        <button onclick="sendShortcut('ctrl+w')">关闭标签页</button>
                        <button onclick="sendShortcut('ctrl+shift+t')">恢复标签页</button>
                        <button onclick="sendShortcut('ctrl+r')">刷新</button>
                        <button onclick="sendShortcut('f5')">F5刷新</button>
                        <button onclick="sendShortcut('f12')">开发者工具</button>
                    </div>
                </div>

                <!-- IDE/编程 -->
                <div class="shortcut-category">
                    <h3>IDE/编程</h3>
                    <div class="shortcut-buttons">
                        <button onclick="sendShortcut('ctrl+shift+p')">命令面板</button>
                        <button onclick="sendShortcut('ctrl+p')">快速打开</button>
                        <button onclick="sendShortcut('ctrl+shift+f')">全局搜索</button>
                        <button onclick="sendShortcut('ctrl+g')">跳转行</button>
                        <button onclick="sendShortcut('ctrl+d')">选择单词</button>
                        <button onclick="sendShortcut('ctrl+/')">注释</button>
                    </div>
                </div>

                <!-- 功能键 -->
                <div class="shortcut-category">
                    <h3>功能键</h3>
                    <div class="shortcut-buttons">
                        <button onclick="sendShortcut('f1')">F1</button>
                        <button onclick="sendShortcut('f2')">F2</button>
                        <button onclick="sendShortcut('f3')">F3</button>
                        <button onclick="sendShortcut('f4')">F4</button>
                        <button onclick="sendShortcut('f5')">F5</button>
                        <button onclick="sendShortcut('f6')">F6</button>
                        <button onclick="sendShortcut('f7')">F7</button>
                        <button onclick="sendShortcut('f8')">F8</button>
                        <button onclick="sendShortcut('f9')">F9</button>
                        <button onclick="sendShortcut('f10')">F10</button>
                        <button onclick="sendShortcut('f11')">F11</button>
                        <button onclick="sendShortcut('f12')">F12</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置界面 -->
        <div class="settings-container hidden" id="settingsContainer">
            <div class="settings-header">
                <button class="back-button" onclick="showMainMenu()">← 返回</button>
                <h2>设置</h2>
            </div>

            <div class="settings-content">
                <div class="setting-group">
                    <h3>连接设置</h3>
                    <div class="setting-item">
                        <label>服务器地址</label>
                        <input type="text" id="serverAddress" readonly>
                    </div>
                    <div class="setting-item">
                        <label>连接状态</label>
                        <span id="connectionInfo">已连接</span>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>配对码连接</h3>
                    <div class="pairing-section">
                        <p>如果无法扫描二维码，可以手动输入配对码连接</p>
                        <div class="pairing-input-group">
                            <input type="text" id="pairingCodeInput" placeholder="请输入6位配对码" maxlength="6"
                                pattern="[0-9]{6}">
                            <button onclick="connectWithPairingCode()">连接</button>
                        </div>
                        <div class="pairing-status" id="pairingStatus"></div>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>关于</h3>
                    <p>KeywordByAny v1.0.0</p>
                    <p>让手机成为你的无线触控板和键盘</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="js/websocket.js"></script>
    <script src="js/touchpad.js"></script>
    <script src="js/keyboard.js"></script>
    <script src="js/shortcuts.js"></script>
    <script src="js/app.js"></script>
</body>

</html>