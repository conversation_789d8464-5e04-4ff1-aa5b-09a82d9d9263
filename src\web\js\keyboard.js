/**
 * 键盘功能管理器
 */
class KeyboardManager {
    constructor() {
        this.textInput = null;
        this.virtualKeyboard = null;
        this.isVirtualKeyboardVisible = false;
        this.lastInputValue = '';
        this.isComposing = false; // 输入法组合状态
        
        this.init();
    }

    init() {
        this.textInput = document.getElementById('textInput');
        this.virtualKeyboard = document.getElementById('virtualKeyboard');
        
        if (this.textInput) {
            this.bindTextInputEvents();
        }
        
        if (this.virtualKeyboard) {
            this.createVirtualKeyboard();
        }
    }

    bindTextInputEvents() {
        // 输入事件
        this.textInput.addEventListener('input', (e) => this.handleTextInput(e));
        this.textInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
        this.textInput.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // 输入法组合事件
        this.textInput.addEventListener('compositionstart', () => {
            this.isComposing = true;
        });
        
        this.textInput.addEventListener('compositionend', (e) => {
            this.isComposing = false;
            this.handleTextInput(e);
        });
        
        // 焦点事件
        this.textInput.addEventListener('focus', () => {
            this.lastInputValue = this.textInput.value;
        });
        
        this.textInput.addEventListener('blur', () => {
            // 失去焦点时可以选择清空输入框
            // this.textInput.value = '';
        });
    }

    handleTextInput(e) {
        if (this.isComposing) {
            return; // 输入法组合中，不发送
        }
        
        const currentValue = this.textInput.value;
        const lastValue = this.lastInputValue;
        
        if (currentValue.length > lastValue.length) {
            // 有新字符输入
            const newText = currentValue.substring(lastValue.length);
            this.sendText(newText);
        } else if (currentValue.length < lastValue.length) {
            // 有字符被删除
            const deletedCount = lastValue.length - currentValue.length;
            for (let i = 0; i < deletedCount; i++) {
                this.sendKey('backspace');
            }
        }
        
        this.lastInputValue = currentValue;
    }

    handleKeyDown(e) {
        // 处理特殊按键
        const key = e.key.toLowerCase();
        const ctrlKey = e.ctrlKey;
        const altKey = e.altKey;
        const shiftKey = e.shiftKey;
        
        // 阻止某些默认行为
        if (key === 'tab' || key === 'escape' || (ctrlKey && ['a', 's', 'z', 'y', 'c', 'v', 'x'].includes(key))) {
            e.preventDefault();
            this.sendKeyWithModifiers(key, ctrlKey, altKey, shiftKey);
        }
    }

    handleKeyUp(e) {
        // 按键释放处理（如果需要）
    }

    sendText(text) {
        if (text && wsManager.isConnected) {
            wsManager.sendKeyPress('', text);
        }
    }

    sendKey(key) {
        if (wsManager.isConnected) {
            wsManager.sendKeyPress(key);
        }
    }

    sendKeyWithModifiers(key, ctrlKey = false, altKey = false, shiftKey = false) {
        if (wsManager.isConnected) {
            wsManager.sendKeyPress(key, '', ctrlKey, altKey, shiftKey);
        }
    }

    createVirtualKeyboard() {
        const keyboardLayout = [
            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'Backspace'],
            ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
            ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'Enter'],
            ['Shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '?'],
            ['Ctrl', 'Alt', 'Space', 'Alt', 'Ctrl']
        ];
        
        this.virtualKeyboard.innerHTML = '';
        
        keyboardLayout.forEach(row => {
            const rowElement = document.createElement('div');
            rowElement.className = 'keyboard-row';
            
            row.forEach(key => {
                const keyElement = document.createElement('button');
                keyElement.className = 'virtual-key';
                keyElement.textContent = key === 'Space' ? '空格' : key;
                keyElement.dataset.key = key;
                
                // 特殊按键样式
                if (['Backspace', 'Enter', 'Shift', 'Ctrl', 'Alt', 'Space'].includes(key)) {
                    keyElement.classList.add('special-key');
                }
                
                if (key === 'Space') {
                    keyElement.classList.add('space-key');
                }
                
                keyElement.addEventListener('touchstart', (e) => this.handleVirtualKeyPress(e, key), { passive: false });
                keyElement.addEventListener('mousedown', (e) => this.handleVirtualKeyPress(e, key));
                
                rowElement.appendChild(keyElement);
            });
            
            this.virtualKeyboard.appendChild(rowElement);
        });
        
        // 添加虚拟键盘样式
        this.addVirtualKeyboardStyles();
    }

    addVirtualKeyboardStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .virtual-keyboard {
                background: #f8f9fa;
                padding: 15px;
                border-top: 1px solid #e9ecef;
            }
            
            .keyboard-row {
                display: flex;
                justify-content: center;
                margin-bottom: 8px;
                gap: 4px;
            }
            
            .virtual-key {
                min-width: 32px;
                height: 40px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.1s;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 8px;
            }
            
            .virtual-key:hover {
                background: #e9ecef;
            }
            
            .virtual-key:active {
                background: #007ACC;
                color: white;
                transform: scale(0.95);
            }
            
            .virtual-key.special-key {
                background: #6c757d;
                color: white;
                font-size: 12px;
            }
            
            .virtual-key.space-key {
                flex: 1;
                max-width: 200px;
            }
            
            @media (max-width: 480px) {
                .virtual-key {
                    min-width: 28px;
                    height: 36px;
                    font-size: 12px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    handleVirtualKeyPress(e, key) {
        e.preventDefault();
        
        // 提供视觉反馈
        const keyElement = e.target;
        keyElement.style.transform = 'scale(0.95)';
        keyElement.style.background = '#007ACC';
        keyElement.style.color = 'white';
        
        setTimeout(() => {
            keyElement.style.transform = '';
            keyElement.style.background = '';
            keyElement.style.color = '';
        }, 150);
        
        // 发送按键
        switch (key) {
            case 'Backspace':
                this.sendKey('backspace');
                break;
            case 'Enter':
                this.sendKey('enter');
                break;
            case 'Space':
                this.sendKey('space');
                break;
            case 'Shift':
                // 切换大小写状态
                this.toggleShift();
                break;
            case 'Ctrl':
            case 'Alt':
                // 修饰键处理
                break;
            default:
                this.sendText(key);
                break;
        }
        
        // 提供触觉反馈
        if (navigator.vibrate) {
            navigator.vibrate(30);
        }
    }

    toggleShift() {
        // 切换虚拟键盘的大小写状态
        const keys = this.virtualKeyboard.querySelectorAll('.virtual-key');
        keys.forEach(key => {
            const keyValue = key.dataset.key;
            if (keyValue && keyValue.length === 1 && /[a-z]/.test(keyValue)) {
                const isUpperCase = key.textContent === keyValue.toUpperCase();
                key.textContent = isUpperCase ? keyValue.toLowerCase() : keyValue.toUpperCase();
                key.dataset.key = isUpperCase ? keyValue.toLowerCase() : keyValue.toUpperCase();
            }
        });
    }

    toggleVirtualKeyboard() {
        this.isVirtualKeyboardVisible = !this.isVirtualKeyboardVisible;
        
        if (this.virtualKeyboard) {
            this.virtualKeyboard.classList.toggle('hidden', !this.isVirtualKeyboardVisible);
        }
        
        // 更新按钮状态
        const toggleButton = document.querySelector('.keyboard-toggle');
        if (toggleButton) {
            toggleButton.style.background = this.isVirtualKeyboardVisible ? 
                'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.2)';
        }
    }

    clearTextInput() {
        if (this.textInput) {
            this.textInput.value = '';
            this.lastInputValue = '';
            this.textInput.focus();
        }
    }

    activate() {
        if (this.textInput) {
            this.textInput.focus();
        }
    }

    deactivate() {
        if (this.textInput) {
            this.textInput.blur();
        }
        
        if (this.isVirtualKeyboardVisible) {
            this.toggleVirtualKeyboard();
        }
    }
}

// 键盘控制函数
function clearTextInput() {
    keyboardManager.clearTextInput();
}

function sendEnter() {
    keyboardManager.sendKey('enter');
}

function sendTab() {
    keyboardManager.sendKey('tab');
}

function sendEscape() {
    keyboardManager.sendKey('escape');
}

function toggleVirtualKeyboard() {
    keyboardManager.toggleVirtualKeyboard();
}

// 创建全局键盘管理器实例
const keyboardManager = new KeyboardManager();
