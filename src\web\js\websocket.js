/**
 * WebSocket通信管理器
 */
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.onConnectionChange = null;
        this.onMessage = null;
        
        this.init();
    }

    init() {
        this.connect();
    }

    connect() {
        try {
            // 获取当前页面的主机和端口
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}/input`;
            
            console.log('连接到WebSocket服务器:', wsUrl);
            
            this.socket = new WebSocket(wsUrl);
            
            this.socket.onopen = () => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.updateConnectionStatus(true);
                
                if (this.onConnectionChange) {
                    this.onConnectionChange(true);
                }
            };
            
            this.socket.onclose = (event) => {
                console.log('WebSocket连接已关闭', event);
                this.isConnected = false;
                this.updateConnectionStatus(false);
                
                if (this.onConnectionChange) {
                    this.onConnectionChange(false);
                }
                
                // 尝试重连
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    setTimeout(() => this.connect(), this.reconnectDelay * this.reconnectAttempts);
                } else {
                    console.log('达到最大重连次数，停止重连');
                    this.showConnectionError();
                }
            };
            
            this.socket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.updateConnectionStatus(false, '连接错误');
            };
            
            this.socket.onmessage = (event) => {
                if (this.onMessage) {
                    try {
                        const data = JSON.parse(event.data);
                        this.onMessage(data);
                    } catch (e) {
                        console.error('解析消息失败:', e);
                    }
                }
            };
            
        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            this.updateConnectionStatus(false, '连接失败');
        }
    }

    send(data) {
        if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {
            try {
                this.socket.send(JSON.stringify(data));
                return true;
            } catch (error) {
                console.error('发送消息失败:', error);
                return false;
            }
        } else {
            console.warn('WebSocket未连接，无法发送消息');
            return false;
        }
    }

    sendInputEvent(type, data = {}) {
        const event = {
            Type: type,
            Timestamp: new Date().toISOString(),
            ...data
        };
        
        return this.send(event);
    }

    sendMouseMove(deltaX, deltaY) {
        return this.sendInputEvent('MouseMove', {
            DeltaX: deltaX,
            DeltaY: deltaY
        });
    }

    sendMouseClick(button) {
        return this.sendInputEvent('MouseClick', {
            Button: button
        });
    }

    sendMouseScroll(deltaY) {
        return this.sendInputEvent('MouseScroll', {
            DeltaY: deltaY
        });
    }

    sendKeyPress(key, text = '', ctrlKey = false, altKey = false, shiftKey = false) {
        return this.sendInputEvent('KeyPress', {
            Key: key,
            Text: text,
            CtrlKey: ctrlKey,
            AltKey: altKey,
            ShiftKey: shiftKey
        });
    }

    sendShortcut(shortcut) {
        return this.sendInputEvent('Shortcut', {
            Key: shortcut
        });
    }

    updateConnectionStatus(connected, message = '') {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const connectionInfo = document.getElementById('connectionInfo');
        
        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${connected ? 'connected' : ''}`;
        }
        
        if (statusText) {
            if (connected) {
                statusText.textContent = '已连接';
            } else {
                statusText.textContent = message || '连接断开';
            }
        }
        
        if (connectionInfo) {
            connectionInfo.textContent = connected ? '已连接' : (message || '连接断开');
        }
    }

    showConnectionError() {
        const errorMessage = `
            <div style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 255, 255, 0.95);
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                text-align: center;
                z-index: 1000;
                max-width: 300px;
            ">
                <h3 style="color: #dc3545; margin-bottom: 15px;">连接失败</h3>
                <p style="margin-bottom: 20px; color: #666;">
                    无法连接到服务器，请确保：<br>
                    1. 电脑端应用正在运行<br>
                    2. 手机和电脑在同一网络<br>
                    3. 防火墙允许连接
                </p>
                <button onclick="location.reload()" style="
                    background: #007ACC;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    cursor: pointer;
                ">重新连接</button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', errorMessage);
    }

    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
    }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager();

// 页面卸载时断开连接
window.addEventListener('beforeunload', () => {
    wsManager.disconnect();
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // 页面隐藏时可以选择断开连接以节省资源
        // wsManager.disconnect();
    } else {
        // 页面显示时重新连接
        if (!wsManager.isConnected) {
            wsManager.connect();
        }
    }
});
