using System;

namespace KeywordByAny.Desktop.Models
{
    /// <summary>
    /// 输入事件类型
    /// </summary>
    public enum InputEventType
    {
        MouseMove,      // 鼠标移动
        MouseClick,     // 鼠标点击
        MouseScroll,    // 鼠标滚轮
        KeyPress,       // 按键按下
        KeyRelease,     // 按键释放
        Shortcut        // 快捷键
    }

    /// <summary>
    /// 鼠标按钮类型
    /// </summary>
    public enum MouseButton
    {
        Left,
        Right,
        Middle
    }

    /// <summary>
    /// 输入事件数据
    /// </summary>
    public class InputEvent
    {
        public InputEventType Type { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
        public double DeltaX { get; set; }
        public double DeltaY { get; set; }
        public MouseButton Button { get; set; }
        public string Key { get; set; }
        public string Text { get; set; }
        public bool CtrlKey { get; set; }
        public bool AltKey { get; set; }
        public bool ShiftKey { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 触摸手势类型
    /// </summary>
    public enum GestureType
    {
        Tap,            // 单击
        DoubleTap,      // 双击
        LongPress,      // 长按
        Swipe,          // 滑动
        Pinch,          // 缩放
        Rotate          // 旋转
    }

    /// <summary>
    /// 触摸手势事件
    /// </summary>
    public class GestureEvent
    {
        public GestureType Type { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
        public double Scale { get; set; }
        public double Rotation { get; set; }
        public double VelocityX { get; set; }
        public double VelocityY { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
