using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace KeywordByAny.Desktop.Services
{
    /// <summary>
    /// 配对码服务
    /// </summary>
    public class PairingCodeService
    {
        private readonly ConcurrentDictionary<string, PairingSession> _activeSessions;
        private readonly Timer _cleanupTimer;
        private readonly Random _random;

        public event Action<string, string> OnPairingRequest;
        public event Action<string> OnPairingSuccess;
        public event Action<string, string> OnPairingFailed;

        public PairingCodeService()
        {
            _activeSessions = new ConcurrentDictionary<string, PairingSession>();
            _random = new Random();

            // 每分钟清理过期的配对会话
            _cleanupTimer = new Timer(CleanupExpiredSessions, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        /// <summary>
        /// 生成新的配对码
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <param name="expirationMinutes">过期时间（分钟）</param>
        /// <returns>配对码</returns>
        public string GeneratePairingCode(string deviceName = "Unknown Device", int expirationMinutes = 10)
        {
            // 生成6位数字配对码
            var code = _random.Next(100000, 999999).ToString();

            var session = new PairingSession
            {
                Code = code,
                DeviceName = deviceName,
                CreatedAt = DateTime.Now,
                ExpiresAt = DateTime.Now.AddMinutes(expirationMinutes),
                IsUsed = false
            };

            _activeSessions.TryAdd(code, session);

            return code;
        }

        /// <summary>
        /// 验证配对码
        /// </summary>
        /// <param name="code">配对码</param>
        /// <param name="clientId">客户端ID</param>
        /// <returns>验证结果</returns>
        public PairingResult ValidatePairingCode(string code, string clientId)
        {
            if (string.IsNullOrWhiteSpace(code) || string.IsNullOrWhiteSpace(clientId))
            {
                return new PairingResult { Success = false, Message = "配对码或客户端ID不能为空" };
            }

            if (!_activeSessions.TryGetValue(code, out var session))
            {
                return new PairingResult { Success = false, Message = "配对码不存在或已过期" };
            }

            if (session.IsUsed)
            {
                return new PairingResult { Success = false, Message = "配对码已被使用" };
            }

            if (DateTime.Now > session.ExpiresAt)
            {
                _activeSessions.TryRemove(code, out _);
                return new PairingResult { Success = false, Message = "配对码已过期" };
            }

            // 标记为已使用
            session.IsUsed = true;
            session.ClientId = clientId;
            session.PairedAt = DateTime.Now;

            // 生成访问令牌
            var accessToken = GenerateAccessToken(clientId, session.DeviceName);

            OnPairingSuccess?.Invoke(clientId);

            return new PairingResult
            {
                Success = true,
                Message = "配对成功",
                AccessToken = accessToken,
                DeviceName = session.DeviceName
            };
        }

        /// <summary>
        /// 获取活动的配对会话
        /// </summary>
        /// <returns>活动会话列表</returns>
        public PairingSession[] GetActiveSessions()
        {
            var activeSessions = new List<PairingSession>();

            foreach (var kvp in _activeSessions)
            {
                var session = kvp.Value;
                if (!session.IsUsed && DateTime.Now <= session.ExpiresAt)
                {
                    activeSessions.Add(session);
                }
            }

            return activeSessions.ToArray();
        }

        /// <summary>
        /// 撤销配对码
        /// </summary>
        /// <param name="code">配对码</param>
        /// <returns>是否成功撤销</returns>
        public bool RevokePairingCode(string code)
        {
            return _activeSessions.TryRemove(code, out _);
        }

        /// <summary>
        /// 清理所有过期的会话
        /// </summary>
        public void ClearExpiredSessions()
        {
            CleanupExpiredSessions(null);
        }

        /// <summary>
        /// 生成访问令牌
        /// </summary>
        private string GenerateAccessToken(string clientId, string deviceName)
        {
            var data = $"{clientId}:{deviceName}:{DateTime.Now:yyyy-MM-dd HH:mm:ss}";
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hash);
        }

        /// <summary>
        /// 清理过期的配对会话
        /// </summary>
        private void CleanupExpiredSessions(object state)
        {
            var now = DateTime.Now;
            var expiredCodes = new List<string>();

            foreach (var kvp in _activeSessions)
            {
                if (now > kvp.Value.ExpiresAt)
                {
                    expiredCodes.Add(kvp.Key);
                }
            }

            foreach (var code in expiredCodes)
            {
                _activeSessions.TryRemove(code, out _);
            }
        }

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
    }

    /// <summary>
    /// 配对会话信息
    /// </summary>
    public class PairingSession
    {
        public string Code { get; set; }
        public string DeviceName { get; set; }
        public string ClientId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public DateTime? PairedAt { get; set; }
        public bool IsUsed { get; set; }

        public TimeSpan TimeRemaining => ExpiresAt > DateTime.Now ? ExpiresAt - DateTime.Now : TimeSpan.Zero;
        public bool IsExpired => DateTime.Now > ExpiresAt;
    }

    /// <summary>
    /// 配对结果
    /// </summary>
    public class PairingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string AccessToken { get; set; }
        public string DeviceName { get; set; }
    }
}
