<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon Condition="Exists('icon.ico')">icon.ico</ApplicationIcon>
    <AssemblyTitle>KeywordByAny Desktop</AssemblyTitle>
    <AssemblyDescription>无线触控板和键盘控制器</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="WebSocketSharp-netstandard" Version="1.0.1" />
    <PackageReference Include="Hardcodet.NotifyIcon.Wpf" Version="1.1.0" />
    <PackageReference Include="QRCoder" Version="1.4.3" />
  </ItemGroup>

  <ItemGroup>
    <None Update="web\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
