using System.ComponentModel;

namespace KeywordByAny.Desktop.Models
{
    /// <summary>
    /// 应用程序设置
    /// </summary>
    public class AppSettings : INotifyPropertyChanged
    {
        private int _port = 8080;
        private double _mouseSensitivity = 1.0;
        private double _scrollSensitivity = 1.0;
        private bool _enableGestures = true;
        private bool _enableShortcuts = true;
        private bool _startWithWindows = false;
        private bool _minimizeToTray = true;

        /// <summary>
        /// WebSocket服务器端口
        /// </summary>
        public int Port
        {
            get => _port;
            set
            {
                _port = value;
                OnPropertyChanged(nameof(Port));
            }
        }

        /// <summary>
        /// 鼠标灵敏度
        /// </summary>
        public double MouseSensitivity
        {
            get => _mouseSensitivity;
            set
            {
                _mouseSensitivity = value;
                OnPropertyChanged(nameof(MouseSensitivity));
            }
        }

        /// <summary>
        /// 滚轮灵敏度
        /// </summary>
        public double ScrollSensitivity
        {
            get => _scrollSensitivity;
            set
            {
                _scrollSensitivity = value;
                OnPropertyChanged(nameof(ScrollSensitivity));
            }
        }

        /// <summary>
        /// 启用手势识别
        /// </summary>
        public bool EnableGestures
        {
            get => _enableGestures;
            set
            {
                _enableGestures = value;
                OnPropertyChanged(nameof(EnableGestures));
            }
        }

        /// <summary>
        /// 启用快捷键
        /// </summary>
        public bool EnableShortcuts
        {
            get => _enableShortcuts;
            set
            {
                _enableShortcuts = value;
                OnPropertyChanged(nameof(EnableShortcuts));
            }
        }

        /// <summary>
        /// 开机自启动
        /// </summary>
        public bool StartWithWindows
        {
            get => _startWithWindows;
            set
            {
                _startWithWindows = value;
                OnPropertyChanged(nameof(StartWithWindows));
            }
        }

        /// <summary>
        /// 最小化到系统托盘
        /// </summary>
        public bool MinimizeToTray
        {
            get => _minimizeToTray;
            set
            {
                _minimizeToTray = value;
                OnPropertyChanged(nameof(MinimizeToTray));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
