[2025-08-06 00:37:58.463] 处理快捷键: 'ctrl+c'
[2025-08-06 00:37:58.543] 分解后的按键: [ctrl, c]
[2025-08-06 00:37:58.545] 发送快捷键组合: 修饰键数量=1, 主键=C
[2025-08-06 00:37:58.548] 按下修饰键: Control<PERSON><PERSON> (键码=17)
[2025-08-06 00:37:58.549] 按下主键: C (键码=67)
[2025-08-06 00:37:58.550] 释放主键: C (键码=67)
[2025-08-06 00:37:58.550] 释放修饰键: Control<PERSON>ey (键码=17)
[2025-08-06 00:37:58.551] 发送 4 个输入事件
[2025-08-06 00:37:58.555] SendInput 返回值: 4
[2025-08-06 00:37:58.556] 快捷键发送完成
[2025-08-06 00:37:59.630] 处理快捷键: 'ctrl+c'
[2025-08-06 00:37:59.631] 分解后的按键: [ctrl, c]
[2025-08-06 00:37:59.632] 发送快捷键组合: 修饰键数量=1, 主键=C
[2025-08-06 00:37:59.632] 按下修饰键: ControlKey (键码=17)
[2025-08-06 00:37:59.632] 按下主键: C (键码=67)
[2025-08-06 00:37:59.633] 释放主键: C (键码=67)
[2025-08-06 00:37:59.633] 释放修饰键: ControlKey (键码=17)
[2025-08-06 00:37:59.634] 发送 4 个输入事件
[2025-08-06 00:37:59.636] SendInput 返回值: 4
[2025-08-06 00:37:59.636] 快捷键发送完成
[2025-08-06 00:38:00.782] 处理快捷键: 'ctrl+c'
[2025-08-06 00:38:00.783] 分解后的按键: [ctrl, c]
[2025-08-06 00:38:00.783] 发送快捷键组合: 修饰键数量=1, 主键=C
[2025-08-06 00:38:00.784] 按下修饰键: ControlKey (键码=17)
[2025-08-06 00:38:00.784] 按下主键: C (键码=67)
[2025-08-06 00:38:00.784] 释放主键: C (键码=67)
[2025-08-06 00:38:00.785] 释放修饰键: ControlKey (键码=17)
[2025-08-06 00:38:00.785] 发送 4 个输入事件
[2025-08-06 00:38:00.789] SendInput 返回值: 4
[2025-08-06 00:38:00.789] 快捷键发送完成
[2025-08-06 00:38:01.246] 处理快捷键: 'ctrl+c'
[2025-08-06 00:38:01.247] 分解后的按键: [ctrl, c]
[2025-08-06 00:38:01.247] 发送快捷键组合: 修饰键数量=1, 主键=C
[2025-08-06 00:38:01.248] 按下修饰键: ControlKey (键码=17)
[2025-08-06 00:38:01.248] 按下主键: C (键码=67)
[2025-08-06 00:38:01.249] 释放主键: C (键码=67)
[2025-08-06 00:38:01.249] 释放修饰键: ControlKey (键码=17)
[2025-08-06 00:38:01.249] 发送 4 个输入事件
[2025-08-06 00:38:01.253] SendInput 返回值: 4
[2025-08-06 00:38:01.254] 快捷键发送完成
[2025-08-06 00:38:07.052] 处理快捷键: 'ctrl+c'
[2025-08-06 00:38:07.052] 分解后的按键: [ctrl, c]
[2025-08-06 00:38:07.053] 发送快捷键组合: 修饰键数量=1, 主键=C
[2025-08-06 00:38:07.053] 按下修饰键: ControlKey (键码=17)
[2025-08-06 00:38:07.054] 按下主键: C (键码=67)
[2025-08-06 00:38:07.054] 释放主键: C (键码=67)
[2025-08-06 00:38:07.054] 释放修饰键: ControlKey (键码=17)
[2025-08-06 00:38:07.054] 发送 4 个输入事件
[2025-08-06 00:38:07.059] SendInput 返回值: 4
[2025-08-06 00:38:07.060] 快捷键发送完成
[2025-08-06 00:38:08.936] 处理快捷键: 'ctrl+v'
[2025-08-06 00:38:08.936] 分解后的按键: [ctrl, v]
[2025-08-06 00:38:08.937] 发送快捷键组合: 修饰键数量=1, 主键=V
[2025-08-06 00:38:08.937] 按下修饰键: ControlKey (键码=17)
[2025-08-06 00:38:08.938] 按下主键: V (键码=86)
[2025-08-06 00:38:08.939] 释放主键: V (键码=86)
[2025-08-06 00:38:08.939] 释放修饰键: ControlKey (键码=17)
[2025-08-06 00:38:08.939] 发送 4 个输入事件
[2025-08-06 00:38:08.943] SendInput 返回值: 4
[2025-08-06 00:38:08.943] 快捷键发送完成
[2025-08-06 00:38:12.256] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:12.257] 分解后的按键: [alt, tab]
[2025-08-06 00:38:12.257] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:12.257] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:12.258] 按下主键: Tab (键码=9)
[2025-08-06 00:38:12.258] 释放主键: Tab (键码=9)
[2025-08-06 00:38:12.258] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:12.259] 发送 4 个输入事件
[2025-08-06 00:38:12.266] SendInput 返回值: 4
[2025-08-06 00:38:12.267] 快捷键发送完成
[2025-08-06 00:38:13.026] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:13.027] 分解后的按键: [alt, tab]
[2025-08-06 00:38:13.027] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:13.027] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:13.027] 按下主键: Tab (键码=9)
[2025-08-06 00:38:13.028] 释放主键: Tab (键码=9)
[2025-08-06 00:38:13.028] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:13.028] 发送 4 个输入事件
[2025-08-06 00:38:13.029] SendInput 返回值: 4
[2025-08-06 00:38:13.030] 快捷键发送完成
[2025-08-06 00:38:13.567] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:13.567] 分解后的按键: [alt, tab]
[2025-08-06 00:38:13.568] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:13.568] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:13.568] 按下主键: Tab (键码=9)
[2025-08-06 00:38:13.569] 释放主键: Tab (键码=9)
[2025-08-06 00:38:13.569] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:13.570] 发送 4 个输入事件
[2025-08-06 00:38:13.577] SendInput 返回值: 4
[2025-08-06 00:38:13.577] 快捷键发送完成
[2025-08-06 00:38:13.738] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:13.738] 分解后的按键: [alt, tab]
[2025-08-06 00:38:13.738] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:13.739] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:13.739] 按下主键: Tab (键码=9)
[2025-08-06 00:38:13.740] 释放主键: Tab (键码=9)
[2025-08-06 00:38:13.740] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:13.741] 发送 4 个输入事件
[2025-08-06 00:38:13.742] SendInput 返回值: 4
[2025-08-06 00:38:13.742] 快捷键发送完成
[2025-08-06 00:38:13.934] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:13.934] 分解后的按键: [alt, tab]
[2025-08-06 00:38:13.935] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:13.935] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:13.936] 按下主键: Tab (键码=9)
[2025-08-06 00:38:13.936] 释放主键: Tab (键码=9)
[2025-08-06 00:38:13.937] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:13.937] 发送 4 个输入事件
[2025-08-06 00:38:13.945] SendInput 返回值: 4
[2025-08-06 00:38:13.946] 快捷键发送完成
[2025-08-06 00:38:16.410] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:16.411] 分解后的按键: [alt, tab]
[2025-08-06 00:38:16.411] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:16.411] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:16.411] 按下主键: Tab (键码=9)
[2025-08-06 00:38:16.411] 释放主键: Tab (键码=9)
[2025-08-06 00:38:16.412] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:16.412] 发送 4 个输入事件
[2025-08-06 00:38:16.413] SendInput 返回值: 4
[2025-08-06 00:38:16.413] 快捷键发送完成
[2025-08-06 00:38:36.473] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:36.473] 分解后的按键: [alt, tab]
[2025-08-06 00:38:36.474] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:36.474] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:36.474] 按下主键: Tab (键码=9)
[2025-08-06 00:38:36.474] 释放主键: Tab (键码=9)
[2025-08-06 00:38:36.475] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:36.475] 发送 4 个输入事件
[2025-08-06 00:38:36.476] SendInput 返回值: 4
[2025-08-06 00:38:36.477] 快捷键发送完成
[2025-08-06 00:38:36.889] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:36.889] 分解后的按键: [alt, tab]
[2025-08-06 00:38:36.890] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:36.891] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:36.891] 按下主键: Tab (键码=9)
[2025-08-06 00:38:36.891] 释放主键: Tab (键码=9)
[2025-08-06 00:38:36.891] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:36.892] 发送 4 个输入事件
[2025-08-06 00:38:36.900] SendInput 返回值: 4
[2025-08-06 00:38:36.901] 快捷键发送完成
[2025-08-06 00:38:37.272] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:37.272] 分解后的按键: [alt, tab]
[2025-08-06 00:38:37.273] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:37.273] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:37.273] 按下主键: Tab (键码=9)
[2025-08-06 00:38:37.273] 释放主键: Tab (键码=9)
[2025-08-06 00:38:37.273] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:37.274] 发送 4 个输入事件
[2025-08-06 00:38:37.274] SendInput 返回值: 4
[2025-08-06 00:38:37.275] 快捷键发送完成
[2025-08-06 00:38:37.616] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:37.617] 分解后的按键: [alt, tab]
[2025-08-06 00:38:37.617] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:37.617] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:37.617] 按下主键: Tab (键码=9)
[2025-08-06 00:38:37.618] 释放主键: Tab (键码=9)
[2025-08-06 00:38:37.618] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:37.618] 发送 4 个输入事件
[2025-08-06 00:38:37.622] SendInput 返回值: 4
[2025-08-06 00:38:37.623] 快捷键发送完成
[2025-08-06 00:38:37.921] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:37.921] 分解后的按键: [alt, tab]
[2025-08-06 00:38:37.921] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:37.921] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:37.922] 按下主键: Tab (键码=9)
[2025-08-06 00:38:37.922] 释放主键: Tab (键码=9)
[2025-08-06 00:38:37.922] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:37.922] 发送 4 个输入事件
[2025-08-06 00:38:37.923] SendInput 返回值: 4
[2025-08-06 00:38:37.923] 快捷键发送完成
[2025-08-06 00:38:38.306] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:38.307] 分解后的按键: [alt, tab]
[2025-08-06 00:38:38.307] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:38.307] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:38.307] 按下主键: Tab (键码=9)
[2025-08-06 00:38:38.308] 释放主键: Tab (键码=9)
[2025-08-06 00:38:38.308] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:38.308] 发送 4 个输入事件
[2025-08-06 00:38:38.314] SendInput 返回值: 4
[2025-08-06 00:38:38.315] 快捷键发送完成
[2025-08-06 00:38:38.671] 处理快捷键: 'alt+tab'
[2025-08-06 00:38:38.672] 分解后的按键: [alt, tab]
[2025-08-06 00:38:38.672] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 00:38:38.672] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:38.672] 按下主键: Tab (键码=9)
[2025-08-06 00:38:38.673] 释放主键: Tab (键码=9)
[2025-08-06 00:38:38.673] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:38.673] 发送 4 个输入事件
[2025-08-06 00:38:38.675] SendInput 返回值: 4
[2025-08-06 00:38:38.675] 快捷键发送完成
[2025-08-06 00:38:40.858] 处理快捷键: 'alt+space'
[2025-08-06 00:38:40.858] 分解后的按键: [alt, space]
[2025-08-06 00:38:40.858] 发送快捷键组合: 修饰键数量=1, 主键=Space
[2025-08-06 00:38:40.859] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:40.859] 按下主键: Space (键码=32)
[2025-08-06 00:38:40.860] 释放主键: Space (键码=32)
[2025-08-06 00:38:40.860] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:40.861] 发送 4 个输入事件
[2025-08-06 00:38:40.864] SendInput 返回值: 4
[2025-08-06 00:38:40.865] 快捷键发送完成
[2025-08-06 00:38:41.619] 处理快捷键: 'alt+space'
[2025-08-06 00:38:41.619] 分解后的按键: [alt, space]
[2025-08-06 00:38:41.619] 发送快捷键组合: 修饰键数量=1, 主键=Space
[2025-08-06 00:38:41.620] 按下修饰键: Menu (键码=18)
[2025-08-06 00:38:41.620] 按下主键: Space (键码=32)
[2025-08-06 00:38:41.620] 释放主键: Space (键码=32)
[2025-08-06 00:38:41.620] 释放修饰键: Menu (键码=18)
[2025-08-06 00:38:41.621] 发送 4 个输入事件
[2025-08-06 00:38:41.623] SendInput 返回值: 4
[2025-08-06 00:38:41.623] 快捷键发送完成
[2025-08-06 02:03:44.197] 处理快捷键: 'alt+tab'
[2025-08-06 02:03:44.304] 分解后的按键: [alt, tab]
[2025-08-06 02:03:44.308] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 02:03:44.309] 按下修饰键: Menu (键码=18)
[2025-08-06 02:03:44.310] 按下主键: Tab (键码=9)
[2025-08-06 02:03:44.310] 释放主键: Tab (键码=9)
[2025-08-06 02:03:44.310] 释放修饰键: Menu (键码=18)
[2025-08-06 02:03:44.310] 发送 4 个输入事件
[2025-08-06 02:03:44.320] SendInput 返回值: 4
[2025-08-06 02:03:44.320] 快捷键发送完成
[2025-08-06 02:04:00.399] 处理快捷键: 'alt+tab'
[2025-08-06 02:04:00.400] 分解后的按键: [alt, tab]
[2025-08-06 02:04:00.400] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 02:04:00.400] 按下修饰键: Menu (键码=18)
[2025-08-06 02:04:00.400] 按下主键: Tab (键码=9)
[2025-08-06 02:04:00.400] 释放主键: Tab (键码=9)
[2025-08-06 02:04:00.401] 释放修饰键: Menu (键码=18)
[2025-08-06 02:04:00.401] 发送 4 个输入事件
[2025-08-06 02:04:00.406] SendInput 返回值: 4
[2025-08-06 02:04:00.407] 快捷键发送完成
[2025-08-06 02:04:05.970] 处理快捷键: 'alt+tab'
[2025-08-06 02:04:05.971] 分解后的按键: [alt, tab]
[2025-08-06 02:04:05.971] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 02:04:05.972] 按下修饰键: Menu (键码=18)
[2025-08-06 02:04:05.972] 按下主键: Tab (键码=9)
[2025-08-06 02:04:05.972] 释放主键: Tab (键码=9)
[2025-08-06 02:04:05.972] 释放修饰键: Menu (键码=18)
[2025-08-06 02:04:05.972] 发送 4 个输入事件
[2025-08-06 02:04:05.978] SendInput 返回值: 4
[2025-08-06 02:04:05.978] 快捷键发送完成
[2025-08-06 02:05:00.776] 处理快捷键: 'alt+tab'
[2025-08-06 02:05:00.776] 分解后的按键: [alt, tab]
[2025-08-06 02:05:00.777] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 02:05:00.778] 按下修饰键: Menu (键码=18)
[2025-08-06 02:05:00.778] 按下主键: Tab (键码=9)
[2025-08-06 02:05:00.778] 释放主键: Tab (键码=9)
[2025-08-06 02:05:00.779] 释放修饰键: Menu (键码=18)
[2025-08-06 02:05:00.779] 发送 4 个输入事件
[2025-08-06 02:05:00.787] SendInput 返回值: 4
[2025-08-06 02:05:00.789] 快捷键发送完成
[2025-08-06 02:05:15.628] 处理快捷键: 'alt+tab'
[2025-08-06 02:05:15.629] 分解后的按键: [alt, tab]
[2025-08-06 02:05:15.629] 发送快捷键组合: 修饰键数量=1, 主键=Tab
[2025-08-06 02:05:15.630] 按下修饰键: Menu (键码=18)
[2025-08-06 02:05:15.630] 按下主键: Tab (键码=9)
[2025-08-06 02:05:15.631] 释放主键: Tab (键码=9)
[2025-08-06 02:05:15.631] 释放修饰键: Menu (键码=18)
[2025-08-06 02:05:15.631] 发送 4 个输入事件
[2025-08-06 02:05:15.640] SendInput 返回值: 4
[2025-08-06 02:05:15.641] 快捷键发送完成
