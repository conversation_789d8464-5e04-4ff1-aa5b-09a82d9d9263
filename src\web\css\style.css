/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 极客风格配色方案 */
    --bg-primary: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --bg-overlay: #30363d;

    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --text-muted: #6e7681;

    --accent-primary: #00d8ff;
    --accent-secondary: #7c3aed;
    --accent-success: #238636;
    --accent-warning: #d29922;
    --accent-danger: #da3633;

    --border-primary: #30363d;
    --border-secondary: #21262d;

    --shadow-primary: rgba(0, 0, 0, 0.3);
    --shadow-secondary: rgba(0, 216, 255, 0.1);

    /* 字体 */
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
}

body {
    font-family: var(--font-sans);
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    position: relative;
}

/* 极客风格背景效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 216, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 216, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 100vw;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 头部样式 */
.header {
    background: var(--bg-secondary);
    backdrop-filter: blur(20px);
    padding: 24px 20px;
    text-align: center;
    border-bottom: 1px solid var(--border-primary);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
}

.header h1 {
    font-size: 2.2rem;
    margin-bottom: 8px;
    font-weight: 700;
    font-family: var(--font-mono);
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.header p {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 16px;
    font-weight: 400;
}

.connection-status {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    font-size: 0.9rem;
    font-family: var(--font-mono);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-danger);
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px currentColor;
}

.status-indicator.connected {
    background: var(--accent-success);
    animation: none;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

/* 主菜单样式 */
.main-menu {
    flex: 1;
    padding: 32px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    max-width: 900px;
    width: 100%;
}

.menu-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 16px;
    padding: 32px 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.menu-item:hover::before {
    opacity: 0.05;
}

.menu-item:hover {
    transform: translateY(-4px);
    border-color: var(--accent-primary);
    box-shadow:
        0 8px 32px var(--shadow-primary),
        0 0 0 1px var(--accent-primary);
}

.menu-item:active {
    transform: translateY(-2px);
}

.menu-item > * {
    position: relative;
    z-index: 1;
}

.menu-icon {
    font-size: 3.5rem;
    margin-bottom: 16px;
    filter: grayscale(0.3);
    transition: filter 0.3s ease;
}

.menu-item:hover .menu-icon {
    filter: grayscale(0);
}

.menu-item h3 {
    font-size: 1.4rem;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 600;
    font-family: var(--font-mono);
}

.menu-item p {
    font-size: 0.95rem;
    color: var(--text-secondary);
    line-height: 1.5;
    font-weight: 400;
}

/* 通用容器样式 */
.touchpad-container,
.keyboard-container,
.shortcuts-container,
.settings-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
    margin: 12px;
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    box-shadow:
        0 8px 32px var(--shadow-primary),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* 头部样式 */
.touchpad-header,
.keyboard-header,
.shortcuts-header,
.settings-header {
    background: var(--bg-tertiary);
    padding: 18px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-primary);
    position: relative;
}

.touchpad-header::before,
.keyboard-header::before,
.shortcuts-header::before,
.settings-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
}

.touchpad-header h2,
.keyboard-header h2,
.shortcuts-header h2,
.settings-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    font-family: var(--font-mono);
    color: var(--text-primary);
}

.back-button {
    background: var(--bg-overlay);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 10px 16px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.9rem;
    font-family: var(--font-mono);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.back-button:hover {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--bg-primary);
    transform: translateX(-2px);
}

.settings-button,
.keyboard-toggle {
    background: var(--bg-overlay);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1.1rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.settings-button:hover,
.keyboard-toggle:hover {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--bg-primary);
    transform: scale(1.05);
}

/* 触控板样式 */
.touchpad-settings {
    background: var(--bg-tertiary);
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-primary);
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    min-width: 120px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-family: var(--font-mono);
    font-weight: 500;
}

.setting-item input[type="range"] {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: var(--bg-overlay);
    outline: none;
    -webkit-appearance: none;
    border: 1px solid var(--border-primary);
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--accent-primary);
    cursor: pointer;
    border: 2px solid var(--bg-secondary);
    box-shadow: 0 2px 8px rgba(0, 216, 255, 0.3);
    transition: all 0.2s ease;
}

.setting-item input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 216, 255, 0.5);
}

.setting-item span {
    min-width: 40px;
    font-size: 0.9rem;
    color: var(--text-primary);
    text-align: right;
    font-family: var(--font-mono);
    font-weight: 600;
}

.touchpad-area {
    flex: 1;
    margin: 24px;
    background: var(--bg-primary);
    border-radius: 16px;
    position: relative;
    min-height: 320px;
    border: 2px dashed var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.touchpad-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(0, 216, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(124, 58, 237, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.touchpad-area:hover {
    border-color: var(--accent-primary);
    background: var(--bg-secondary);
}

.touchpad-hint {
    text-align: center;
    color: var(--text-muted);
    font-size: 0.9rem;
    line-height: 1.8;
    pointer-events: none;
    font-family: var(--font-mono);
    position: relative;
    z-index: 1;
}

.touchpad-buttons {
    display: flex;
    gap: 12px;
    padding: 24px;
    background: var(--bg-tertiary);
}

.touchpad-btn {
    flex: 1;
    padding: 16px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    background: var(--bg-overlay);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: var(--font-mono);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    touch-action: manipulation;
    position: relative;
    overflow: hidden;
}

.touchpad-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    opacity: 0;
    transition: opacity 0.2s ease;
}

.touchpad-btn:hover::before {
    opacity: 0.1;
}

.touchpad-btn:hover {
    border-color: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 216, 255, 0.2);
}

.touchpad-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 216, 255, 0.3);
}

.touchpad-btn span {
    position: relative;
    z-index: 1;
}

/* 键盘样式 */
.text-input-area {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

#textInput {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-size: 1rem;
    font-family: inherit;
    resize: none;
    outline: none;
    min-height: 200px;
    background: white;
}

#textInput:focus {
    border-color: #007ACC;
}

.input-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.input-controls button {
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    background: #6c757d;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s;
}

.input-controls button:hover {
    background: #5a6268;
}

/* 快捷键样式 */
.shortcuts-grid {
    padding: 24px;
    overflow-y: auto;
}

.shortcut-category {
    margin-bottom: 32px;
}

.shortcut-category h3 {
    font-size: 1.2rem;
    margin-bottom: 16px;
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-weight: 600;
    position: relative;
    padding-bottom: 8px;
}

.shortcut-category h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 1px;
}

.shortcut-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
}

.shortcut-buttons button {
    padding: 14px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 10px;
    background: var(--bg-overlay);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 0.9rem;
    font-family: var(--font-mono);
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    overflow: hidden;
}

.shortcut-buttons button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    opacity: 0;
    transition: opacity 0.2s ease;
}

.shortcut-buttons button:hover::before {
    opacity: 0.1;
}

.shortcut-buttons button:hover {
    border-color: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 216, 255, 0.2);
}

.shortcut-buttons button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 216, 255, 0.3);
}

.shortcut-buttons button span {
    position: relative;
    z-index: 1;
}

/* 设置样式 */
.settings-content {
    padding: 24px;
    overflow-y: auto;
}

.setting-group {
    margin-bottom: 32px;
}

.setting-group h3 {
    font-size: 1.2rem;
    margin-bottom: 16px;
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-weight: 600;
    position: relative;
    padding-bottom: 8px;
}

.setting-group h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 1px;
}

.setting-group .setting-item {
    margin-bottom: 16px;
}

.setting-group input[type="text"] {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 0.9rem;
    font-family: var(--font-mono);
    background: var(--bg-overlay);
    color: var(--text-primary);
    transition: border-color 0.2s ease;
}

.setting-group input[type="text"]:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(0, 216, 255, 0.2);
}

/* 配对码样式 */
.pairing-section {
    background: var(--bg-overlay);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 20px;
    margin-top: 16px;
}

.pairing-section p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 16px;
    line-height: 1.5;
}

.pairing-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 16px;
}

.pairing-input-group input {
    flex: 1;
    padding: 14px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 1.1rem;
    font-family: var(--font-mono);
    font-weight: 600;
    background: var(--bg-primary);
    color: var(--text-primary);
    text-align: center;
    letter-spacing: 2px;
    transition: all 0.2s ease;
}

.pairing-input-group input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(0, 216, 255, 0.2);
    transform: scale(1.02);
}

.pairing-input-group button {
    padding: 14px 24px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--accent-primary);
    color: var(--bg-primary);
    font-size: 0.9rem;
    font-family: var(--font-mono);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.pairing-input-group button:hover {
    background: var(--accent-secondary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 216, 255, 0.3);
}

.pairing-input-group button:active {
    transform: translateY(0);
}

.pairing-status {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-family: var(--font-mono);
    text-align: center;
    transition: all 0.3s ease;
}

.pairing-status.success {
    background: rgba(35, 134, 54, 0.2);
    border: 1px solid var(--accent-success);
    color: var(--accent-success);
}

.pairing-status.error {
    background: rgba(218, 54, 51, 0.2);
    border: 1px solid var(--accent-danger);
    color: var(--accent-danger);
}

.pairing-status.info {
    background: rgba(0, 216, 255, 0.2);
    border: 1px solid var(--accent-primary);
    color: var(--accent-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .menu-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .menu-item {
        padding: 25px 15px;
    }
    
    .shortcut-buttons {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
    }
    
    .shortcut-buttons button {
        padding: 10px 6px;
        font-size: 0.8rem;
    }
    
    .touchpad-buttons {
        flex-direction: column;
    }
    
    .input-controls {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .main-menu {
        padding: 20px 15px;
    }
    
    .touchpad-container,
    .keyboard-container,
    .shortcuts-container,
    .settings-container {
        margin: 5px;
    }
}
