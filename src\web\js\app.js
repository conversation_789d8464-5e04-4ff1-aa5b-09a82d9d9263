/**
 * 主应用程序管理器
 */
class AppManager {
    constructor() {
        this.currentView = 'main';
        this.isFullscreen = false;
        this.settings = {
            mouseSensitivity: 1.0,
            scrollSensitivity: 1.0,
            enableVibration: true,
            enableSounds: false
        };

        this.init();
    }

    init() {
        this.loadSettings();
        this.bindEvents();
        this.setupPWA();
        this.showMainMenu();

        // 设置服务器地址显示
        this.updateServerAddress();

        // 监听连接状态变化
        wsManager.onConnectionChange = (connected) => {
            this.handleConnectionChange(connected);
        };
    }

    loadSettings() {
        try {
            const saved = localStorage.getItem('keywordByAny_settings');
            if (saved) {
                this.settings = {
                    ...this.settings,
                    ...JSON.parse(saved)
                };
            }
        } catch (e) {
            console.warn('加载设置失败:', e);
        }
    }

    saveSettings() {
        try {
            localStorage.setItem('keywordByAny_settings', JSON.stringify(this.settings));
        } catch (e) {
            console.warn('保存设置失败:', e);
        }
    }

    bindEvents() {
        // 阻止页面滚动和缩放
        document.addEventListener('touchmove', (e) => {
            if (e.target.closest('.text-input-area') || e.target.closest('.settings-content')) {
                return; // 允许文本输入区域和设置区域滚动
            }
            e.preventDefault();
        }, {
            passive: false
        });

        // 阻止双击缩放
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, {
            passive: false
        });

        // 阻止长按菜单
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleGlobalKeyDown(e);
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // 屏幕方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleOrientationChange(), 100);
        });

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    setupPWA() {
        // 注册Service Worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(err => {
                console.log('Service Worker注册失败:', err);
            });
        }

        // 添加到主屏幕提示
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            this.showInstallPrompt(deferredPrompt);
        });
    }

    showInstallPrompt(deferredPrompt) {
        // 创建安装提示
        const installBanner = document.createElement('div');
        installBanner.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 122, 204, 0.95);
            color: white;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 1000;
            font-size: 14px;
        `;

        installBanner.innerHTML = `
            <span>添加到主屏幕以获得更好的体验</span>
            <div>
                <button id="installBtn" style="background: white; color: #007ACC; border: none; padding: 8px 12px; border-radius: 4px; margin-right: 8px; cursor: pointer;">安装</button>
                <button id="dismissBtn" style="background: transparent; color: white; border: 1px solid white; padding: 8px 12px; border-radius: 4px; cursor: pointer;">忽略</button>
            </div>
        `;

        document.body.appendChild(installBanner);

        document.getElementById('installBtn').addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('用户接受了安装提示');
                }
                deferredPrompt = null;
                installBanner.remove();
            });
        });

        document.getElementById('dismissBtn').addEventListener('click', () => {
            installBanner.remove();
        });

        // 10秒后自动隐藏
        setTimeout(() => {
            if (installBanner.parentNode) {
                installBanner.remove();
            }
        }, 10000);
    }

    handleConnectionChange(connected) {
        if (connected) {
            this.showNotification('已连接到服务器', 'success');
        } else {
            this.showNotification('与服务器断开连接', 'error');
        }
    }

    handleGlobalKeyDown(e) {
        // 全局快捷键处理
        if (e.key === 'Escape') {
            this.showMainMenu();
        }
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停某些功能
            if (touchpadManager) {
                touchpadManager.deactivate();
            }
        } else {
            // 页面显示时恢复功能
            if (this.currentView === 'touchpad' && touchpadManager) {
                touchpadManager.activate();
            }
        }
    }

    handleOrientationChange() {
        // 屏幕方向变化处理
        this.handleResize();
    }

    handleResize() {
        // 窗口大小变化处理
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    updateServerAddress() {
        const serverAddressInput = document.getElementById('serverAddress');
        if (serverAddressInput) {
            serverAddressInput.value = window.location.origin;
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007ACC'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 1000;
            pointer-events: none;
            animation: slideDown 0.3s ease-out;
        `;
        notification.textContent = message;

        // 添加动画样式
        if (!document.getElementById('notificationStyle')) {
            const style = document.createElement('style');
            style.id = 'notificationStyle';
            style.textContent = `
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideDown 0.3s ease-out reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }

    // 视图切换方法
    showMainMenu() {
        this.hideAllViews();
        document.getElementById('mainMenu').classList.remove('hidden');
        this.currentView = 'main';

        // 停用所有管理器
        if (touchpadManager) touchpadManager.deactivate();
        if (keyboardManager) keyboardManager.deactivate();
    }

    showTouchpad() {
        this.hideAllViews();
        document.getElementById('touchpadContainer').classList.remove('hidden');
        this.currentView = 'touchpad';

        if (touchpadManager) {
            touchpadManager.activate();
        }
    }

    showKeyboard() {
        this.hideAllViews();
        document.getElementById('keyboardContainer').classList.remove('hidden');
        this.currentView = 'keyboard';

        if (keyboardManager) {
            keyboardManager.activate();
        }
    }

    showShortcuts() {
        this.hideAllViews();
        document.getElementById('shortcutsContainer').classList.remove('hidden');
        this.currentView = 'shortcuts';

        // 更新最近使用的快捷键
        if (shortcutsManager) {
            shortcutsManager.updateRecentShortcutsUI();
        }
    }

    showSettings() {
        this.hideAllViews();
        document.getElementById('settingsContainer').classList.remove('hidden');
        this.currentView = 'settings';
    }

    hideAllViews() {
        const views = [
            'mainMenu',
            'touchpadContainer',
            'keyboardContainer',
            'shortcutsContainer',
            'settingsContainer'
        ];

        views.forEach(viewId => {
            const element = document.getElementById(viewId);
            if (element) {
                element.classList.add('hidden');
            }
        });
    }

    toggleTouchpadSettings() {
        const settings = document.getElementById('touchpadSettings');
        if (settings) {
            settings.classList.toggle('hidden');
        }
    }

    toggleFullscreen() {
        if (!this.isFullscreen) {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
        this.isFullscreen = !this.isFullscreen;
    }
}

// 配对码连接功能
function connectWithPairingCode() {
    const pairingCodeInput = document.getElementById('pairingCodeInput');
    const pairingStatus = document.getElementById('pairingStatus');
    const code = pairingCodeInput.value.trim();

    // 验证配对码格式
    if (!/^\d{6}$/.test(code)) {
        showPairingStatus('请输入6位数字配对码', 'error');
        return;
    }

    showPairingStatus('正在验证配对码...', 'info');

    // 发送配对请求
    if (wsManager.isConnected) {
        wsManager.send({
            Type: 'PairingRequest',
            Code: code,
            DeviceName: getDeviceName(),
            Timestamp: new Date().toISOString()
        });

        // 设置超时
        setTimeout(() => {
            if (pairingStatus.classList.contains('info')) {
                showPairingStatus('配对超时，请重试', 'error');
            }
        }, 10000);
    } else {
        showPairingStatus('未连接到服务器', 'error');
    }
}

function showPairingStatus(message, type) {
    const pairingStatus = document.getElementById('pairingStatus');
    pairingStatus.textContent = message;
    pairingStatus.className = `pairing-status ${type}`;

    if (type === 'success') {
        setTimeout(() => {
            pairingStatus.textContent = '';
            pairingStatus.className = 'pairing-status';
        }, 3000);
    }
}

function getDeviceName() {
    const userAgent = navigator.userAgent;
    if (/iPhone|iPad|iPod/.test(userAgent)) {
        return 'iPhone/iPad';
    } else if (/Android/.test(userAgent)) {
        return 'Android设备';
    } else if (/Windows/.test(userAgent)) {
        return 'Windows设备';
    } else if (/Mac/.test(userAgent)) {
        return 'Mac设备';
    } else {
        return '移动设备';
    }
}

// 检查URL参数中的配对码
function checkUrlPairingCode() {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');

    if (code && /^\d{6}$/.test(code)) {
        // 自动填入配对码
        const pairingCodeInput = document.getElementById('pairingCodeInput');
        if (pairingCodeInput) {
            pairingCodeInput.value = code;
        }

        // 显示提示
        appManager.showNotification('检测到配对码，请在设置中点击连接', 'info');

        // 3秒后自动尝试连接
        setTimeout(() => {
            if (wsManager.isConnected) {
                connectWithPairingCode();
            }
        }, 3000);
    }
}

// 全局函数
function openTouchpad() {
    appManager.showTouchpad();
}

function openKeyboard() {
    appManager.showKeyboard();
}

function openShortcuts() {
    appManager.showShortcuts();
}

function openSettings() {
    appManager.showSettings();
}

function showMainMenu() {
    appManager.showMainMenu();
}

function toggleTouchpadSettings() {
    appManager.toggleTouchpadSettings();
}

// 创建全局应用管理器实例
const appManager = new AppManager();

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('KeywordByAny Web客户端已加载');

    // 设置初始视口高度
    appManager.handleResize();

    // 检查URL中的配对码
    checkUrlPairingCode();

    // 显示加载完成提示
    setTimeout(() => {
        if (wsManager.isConnected) {
            appManager.showNotification('应用已就绪', 'success');
        }
    }, 1000);
});