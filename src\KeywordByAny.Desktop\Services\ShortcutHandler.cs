using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace KeywordByAny.Desktop.Services
{
    /// <summary>
    /// 快捷键处理器
    /// </summary>
    public class ShortcutHandler
    {
        private readonly InputSimulator _inputSimulator;
        private readonly Dictionary<string, Action> _shortcuts;

        public ShortcutHandler(InputSimulator inputSimulator)
        {
            _inputSimulator = inputSimulator;
            _shortcuts = InitializeShortcuts();
        }

        /// <summary>
        /// 初始化快捷键映射
        /// </summary>
        private Dictionary<string, Action> InitializeShortcuts()
        {
            return new Dictionary<string, Action>
            {
                // 基本编辑快捷键
                ["ctrl+c"] = () => SendShortcut(Keys.Control, Keys.C),
                ["ctrl+v"] = () => SendShortcut(Keys.Control, Keys.V),
                ["ctrl+x"] = () => SendShortcut(Keys.Control, Keys.X),
                ["ctrl+z"] = () => SendShortcut(Keys.Control, Keys.Z),
                ["ctrl+y"] = () => SendShortcut(Keys.Control, Keys.Y),
                ["ctrl+a"] = () => SendShortcut(Keys.Control, Keys.A),
                ["ctrl+s"] = () => SendShortcut(Keys.Control, Keys.S),
                ["ctrl+f"] = () => SendShortcut(Keys.Control, Keys.F),
                ["ctrl+h"] = () => SendShortcut(Keys.Control, Keys.H),
                ["ctrl+n"] = () => SendShortcut(Keys.Control, Keys.N),
                ["ctrl+o"] = () => SendShortcut(Keys.Control, Keys.O),

                // 窗口管理
                ["alt+tab"] = () => SendShortcut(Keys.Alt, Keys.Tab),
                ["alt+f4"] = () => SendShortcut(Keys.Alt, Keys.F4),
                ["win+d"] = () => SendShortcut(Keys.LWin, Keys.D),
                ["win+l"] = () => SendShortcut(Keys.LWin, Keys.L),
                ["win+r"] = () => SendShortcut(Keys.LWin, Keys.R),

                // 浏览器快捷键
                ["ctrl+t"] = () => SendShortcut(Keys.Control, Keys.T),
                ["ctrl+w"] = () => SendShortcut(Keys.Control, Keys.W),
                ["ctrl+shift+t"] = () => SendShortcut(Keys.Control, Keys.Shift, Keys.T),
                ["ctrl+r"] = () => SendShortcut(Keys.Control, Keys.R),
                ["f5"] = () => _inputSimulator.SendKey(Keys.F5),
                ["ctrl+l"] = () => SendShortcut(Keys.Control, Keys.L),

                // 开发者工具
                ["f12"] = () => _inputSimulator.SendKey(Keys.F12),
                ["ctrl+shift+i"] = () => SendShortcut(Keys.Control, Keys.Shift, Keys.I),
                ["ctrl+shift+j"] = () => SendShortcut(Keys.Control, Keys.Shift, Keys.J),
                ["ctrl+shift+c"] = () => SendShortcut(Keys.Control, Keys.Shift, Keys.C),

                // IDE快捷键
                ["ctrl+shift+p"] = () => SendShortcut(Keys.Control, Keys.Shift, Keys.P),
                ["ctrl+p"] = () => SendShortcut(Keys.Control, Keys.P),
                ["ctrl+shift+f"] = () => SendShortcut(Keys.Control, Keys.Shift, Keys.F),
                ["ctrl+g"] = () => SendShortcut(Keys.Control, Keys.G),
                ["ctrl+d"] = () => SendShortcut(Keys.Control, Keys.D),
                ["ctrl+/"] = () => SendShortcut(Keys.Control, Keys.OemQuestion),
                ["ctrl+shift+/"] = () => SendShortcut(Keys.Control, Keys.Shift, Keys.OemQuestion),

                // 导航快捷键
                ["ctrl+home"] = () => SendShortcut(Keys.Control, Keys.Home),
                ["ctrl+end"] = () => SendShortcut(Keys.Control, Keys.End),
                ["ctrl+left"] = () => SendShortcut(Keys.Control, Keys.Left),
                ["ctrl+right"] = () => SendShortcut(Keys.Control, Keys.Right),
                ["shift+home"] = () => SendShortcut(Keys.Shift, Keys.Home),
                ["shift+end"] = () => SendShortcut(Keys.Shift, Keys.End),

                // 功能键
                ["f1"] = () => _inputSimulator.SendKey(Keys.F1),
                ["f2"] = () => _inputSimulator.SendKey(Keys.F2),
                ["f3"] = () => _inputSimulator.SendKey(Keys.F3),
                ["f4"] = () => _inputSimulator.SendKey(Keys.F4),
                ["f6"] = () => _inputSimulator.SendKey(Keys.F6),
                ["f7"] = () => _inputSimulator.SendKey(Keys.F7),
                ["f8"] = () => _inputSimulator.SendKey(Keys.F8),
                ["f9"] = () => _inputSimulator.SendKey(Keys.F9),
                ["f10"] = () => _inputSimulator.SendKey(Keys.F10),
                ["f11"] = () => _inputSimulator.SendKey(Keys.F11),

                // 特殊按键
                ["escape"] = () => _inputSimulator.SendKey(Keys.Escape),
                ["enter"] = () => _inputSimulator.SendKey(Keys.Enter),
                ["tab"] = () => _inputSimulator.SendKey(Keys.Tab),
                ["shift+tab"] = () => SendShortcut(Keys.Shift, Keys.Tab),
                ["backspace"] = () => _inputSimulator.SendKey(Keys.Back),
                ["delete"] = () => _inputSimulator.SendKey(Keys.Delete),
                ["space"] = () => _inputSimulator.SendKey(Keys.Space),

                // 方向键
                ["up"] = () => _inputSimulator.SendKey(Keys.Up),
                ["down"] = () => _inputSimulator.SendKey(Keys.Down),
                ["left"] = () => _inputSimulator.SendKey(Keys.Left),
                ["right"] = () => _inputSimulator.SendKey(Keys.Right),
                ["pageup"] = () => _inputSimulator.SendKey(Keys.PageUp),
                ["pagedown"] = () => _inputSimulator.SendKey(Keys.PageDown),
                ["home"] = () => _inputSimulator.SendKey(Keys.Home),
                ["end"] = () => _inputSimulator.SendKey(Keys.End)
            };
        }

        /// <summary>
        /// 处理快捷键
        /// </summary>
        public void HandleShortcut(string shortcut)
        {
            if (_shortcuts.TryGetValue(shortcut.ToLower(), out var action))
            {
                action.Invoke();
            }
        }

        /// <summary>
        /// 处理按键组合
        /// </summary>
        public void HandleKey(string key, bool ctrlKey, bool altKey, bool shiftKey)
        {
            var modifiers = new List<string>();
            if (ctrlKey) modifiers.Add("ctrl");
            if (altKey) modifiers.Add("alt");
            if (shiftKey) modifiers.Add("shift");

            string shortcut = string.Join("+", modifiers) + (modifiers.Count > 0 ? "+" : "") + key.ToLower();
            HandleShortcut(shortcut);
        }

        /// <summary>
        /// 发送快捷键组合
        /// </summary>
        private void SendShortcut(params Keys[] keys)
        {
            // 按下所有键
            foreach (var key in keys)
            {
                _inputSimulator.SendKey(key);
            }

            // 释放所有键（逆序）
            for (int i = keys.Length - 1; i >= 0; i--)
            {
                _inputSimulator.SendKey(keys[i], true);
            }
        }
    }
}
