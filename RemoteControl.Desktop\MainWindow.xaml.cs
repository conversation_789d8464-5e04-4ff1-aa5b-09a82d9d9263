using System;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using System.Windows;
using System.Windows.Media;
using RemoteControl.Desktop.Services;

namespace RemoteControl.Desktop
{
    public partial class MainWindow : Window
    {
        private SimpleServer _server;
        private SettingsService _settingsService;
        private int _connectedClients = 0;

        public MainWindow()
        {
            InitializeComponent();

            _settingsService = new SettingsService();
            _server = new SimpleServer(_settingsService.Settings);
            _server.OnLog += OnServerLog;
            _server.OnClientConnected += OnClientConnected;
            _server.OnClientDisconnected += OnClientDisconnected;

            // 监听设置变化
            _settingsService.SettingsChanged += OnSettingsChanged;

            UpdateUI();

            // 如果设置了自动启动，则启动服务器
            if (_settingsService.Settings.AutoStart)
            {
                try
                {
                    _server.Start();
                    UpdateUI();
                }
                catch (Exception ex)
                {
                    OnServerLog($"自动启动服务器失败: {ex.Message}");
                }
            }
        }

        private void StartStopButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_server.IsRunning)
                {
                    _server.Stop();
                }
                else
                {
                    _server.Start();
                }
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenBrowserButton_Click(object sender, RoutedEventArgs e)
        {
            if (_server.IsRunning)
            {
                string url = $"http://{GetLocalIPAddress()}:{_settingsService.Settings.Port}";
                try
                {
                    Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"无法打开浏览器: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                MessageBox.Show("请先启动服务器", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void UpdateUI()
        {
            bool isRunning = _server.IsRunning;

            StartStopButton.Content = isRunning ? "停止服务器" : "启动服务器";
            StartStopButton.Background = isRunning ? new SolidColorBrush(Color.FromRgb(231, 76, 60)) : new SolidColorBrush(Color.FromRgb(52, 152, 219));

            StatusText.Text = isRunning ? "服务器运行中" : "服务器已停止";
            StatusIndicator.Fill = isRunning ? new SolidColorBrush(Color.FromRgb(39, 174, 96)) : new SolidColorBrush(Color.FromRgb(231, 76, 60));

            if (isRunning)
            {
                IpAddressText.Text = $"服务器地址: http://{GetLocalIPAddress()}:{_settingsService.Settings.Port}";
            }
            else
            {
                IpAddressText.Text = "服务器地址: 未启动";
            }

            ConnectedClientsText.Text = $"已连接设备: {_connectedClients}";
        }

        private void OnServerLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                // 可以在这里添加日志显示
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
            });
        }

        private void OnClientConnected()
        {
            Dispatcher.Invoke(() =>
            {
                _connectedClients++;
                UpdateUI();
            });
        }

        private void OnClientDisconnected()
        {
            Dispatcher.Invoke(() =>
            {
                _connectedClients = Math.Max(0, _connectedClients - 1);
                UpdateUI();
            });
        }

        private string GetLocalIPAddress()
        {
            try
            {
                using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, 0);
                socket.Connect("*******", 65530);
                var endPoint = socket.LocalEndPoint as IPEndPoint;
                return endPoint?.Address.ToString() ?? "localhost";
            }
            catch
            {
                return "localhost";
            }
        }

        private void OnSettingsChanged(Models.AppSettings settings)
        {
            // 当设置改变时，如果服务器正在运行且端口改变了，需要重启服务器
            if (_server.IsRunning)
            {
                UpdateUI();
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new SettingsWindow(_settingsService)
            {
                Owner = this
            };

            if (settingsWindow.ShowDialog() == true)
            {
                // 设置已保存，检查是否需要重启服务器
                if (_server.IsRunning)
                {
                    try
                    {
                        _server.Stop();
                        _server = new SimpleServer(_settingsService.Settings);
                        _server.OnLog += OnServerLog;
                        _server.OnClientConnected += OnClientConnected;
                        _server.OnClientDisconnected += OnClientDisconnected;
                        _server.Start();
                        OnServerLog("服务器已重启以应用新设置");
                    }
                    catch (Exception ex)
                    {
                        OnServerLog($"重启服务器失败: {ex.Message}");
                    }
                }
                UpdateUI();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _server?.Stop();
            base.OnClosed(e);
        }
    }
}
