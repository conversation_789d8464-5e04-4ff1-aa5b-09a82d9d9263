using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using KeywordByAny.Desktop.Services;

namespace KeywordByAny.Desktop.Views
{
    public partial class QRCodeWindow : Window
    {
        private readonly string _serverUrl;
        private readonly PairingCodeService _pairingService;
        private string _currentPairingCode;
        private DispatcherTimer _countdownTimer;
        private DateTime _expirationTime;

        public QRCodeWindow(string serverUrl, PairingCodeService pairingService)
        {
            InitializeComponent();
            _serverUrl = serverUrl;
            _pairingService = pairingService;
            
            Loaded += QRCodeWindow_Loaded;
        }

        private async void QRCodeWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await GenerateQRCodeAsync();
            StartCountdownTimer();
        }

        private async Task GenerateQRCodeAsync()
        {
            try
            {
                LoadingPanel.Visibility = Visibility.Visible;
                QRCodeImage.Visibility = Visibility.Collapsed;

                // 生成配对码
                _currentPairingCode = _pairingService.GeneratePairingCode("Mobile Device", 10);
                _expirationTime = DateTime.Now.AddMinutes(10);

                // 构建连接URL（包含配对码）
                var connectionUrl = $"{_serverUrl}?code={_currentPairingCode}";

                // 在后台线程生成二维码
                var qrCodeImage = await Task.Run(() => 
                    QRCodeService.GenerateGeekStyleQRCode(connectionUrl, 8));

                // 更新UI
                QRCodeImage.Source = qrCodeImage;
                ServerAddressText.Text = _serverUrl;
                PairingCodeText.Text = _currentPairingCode;
                
                LoadingPanel.Visibility = Visibility.Collapsed;
                QRCodeImage.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成二维码失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                
                LoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void StartCountdownTimer()
        {
            _countdownTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            
            _countdownTimer.Tick += (s, e) =>
            {
                var remaining = _expirationTime - DateTime.Now;
                
                if (remaining.TotalSeconds <= 0)
                {
                    ExpirationText.Text = "已过期";
                    ExpirationText.Foreground = FindResource("AccentDanger") as System.Windows.Media.Brush;
                    _countdownTimer.Stop();
                    
                    // 显示过期提示
                    MessageBox.Show("二维码已过期，请刷新后重试", "提示", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    var minutes = (int)remaining.TotalMinutes;
                    var seconds = remaining.Seconds;
                    ExpirationText.Text = $"{minutes}分{seconds:D2}秒";
                    
                    // 最后1分钟时变红色
                    if (remaining.TotalMinutes <= 1)
                    {
                        ExpirationText.Foreground = FindResource("AccentDanger") as System.Windows.Media.Brush;
                    }
                }
            };
            
            _countdownTimer.Start();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // 撤销旧的配对码
            if (!string.IsNullOrEmpty(_currentPairingCode))
            {
                _pairingService.RevokePairingCode(_currentPairingCode);
            }
            
            // 停止倒计时
            _countdownTimer?.Stop();
            
            // 重新生成
            await GenerateQRCodeAsync();
            StartCountdownTimer();
        }

        private void CopyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var connectionInfo = $"服务器地址: {_serverUrl}\n配对码: {_currentPairingCode}";
                Clipboard.SetText(connectionInfo);
                
                // 显示复制成功提示
                var originalContent = CopyButton.Content;
                CopyButton.Content = "已复制!";
                CopyButton.IsEnabled = false;
                
                var timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(2)
                };
                timer.Tick += (s, e) =>
                {
                    CopyButton.Content = originalContent;
                    CopyButton.IsEnabled = true;
                    timer.Stop();
                };
                timer.Start();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            // 清理资源
            _countdownTimer?.Stop();
            
            // 撤销配对码
            if (!string.IsNullOrEmpty(_currentPairingCode))
            {
                _pairingService.RevokePairingCode(_currentPairingCode);
            }
            
            base.OnClosed(e);
        }
    }
}
