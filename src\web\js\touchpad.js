/**
 * 触控板功能管理器
 */
class TouchpadManager {
    constructor() {
        this.touchpadArea = null;
        this.isActive = false;
        this.lastTouch = null;
        this.touchStartTime = 0;
        this.longPressTimer = null;
        this.longPressDelay = 500; // 长按延迟
        this.clickThreshold = 10; // 点击移动阈值
        this.scrollSensitivity = 1;
        this.mouseSensitivity = 1;
        this.isScrolling = false;
        this.touches = new Map(); // 存储多点触控信息
        
        this.init();
    }

    init() {
        this.touchpadArea = document.getElementById('touchpadArea');
        if (this.touchpadArea) {
            this.bindEvents();
        }
        
        // 绑定设置控件
        this.bindSettings();
    }

    bindEvents() {
        // 触摸事件
        this.touchpadArea.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
        this.touchpadArea.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        this.touchpadArea.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });
        this.touchpadArea.addEventListener('touchcancel', (e) => this.handleTouchEnd(e), { passive: false });
        
        // 鼠标事件（用于桌面测试）
        this.touchpadArea.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.touchpadArea.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.touchpadArea.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.touchpadArea.addEventListener('wheel', (e) => this.handleWheel(e), { passive: false });
        
        // 阻止默认行为
        this.touchpadArea.addEventListener('contextmenu', (e) => e.preventDefault());
        this.touchpadArea.addEventListener('selectstart', (e) => e.preventDefault());
    }

    bindSettings() {
        const mouseSensitivitySlider = document.getElementById('mouseSensitivity');
        const scrollSensitivitySlider = document.getElementById('scrollSensitivity');
        const mouseSensitivityValue = document.getElementById('mouseSensitivityValue');
        const scrollSensitivityValue = document.getElementById('scrollSensitivityValue');
        
        if (mouseSensitivitySlider) {
            mouseSensitivitySlider.addEventListener('input', (e) => {
                this.mouseSensitivity = parseFloat(e.target.value);
                if (mouseSensitivityValue) {
                    mouseSensitivityValue.textContent = this.mouseSensitivity.toFixed(1);
                }
            });
        }
        
        if (scrollSensitivitySlider) {
            scrollSensitivitySlider.addEventListener('input', (e) => {
                this.scrollSensitivity = parseFloat(e.target.value);
                if (scrollSensitivityValue) {
                    scrollSensitivityValue.textContent = this.scrollSensitivity.toFixed(1);
                }
            });
        }
    }

    handleTouchStart(e) {
        e.preventDefault();
        
        const touches = Array.from(e.touches);
        this.updateTouches(touches);
        
        if (touches.length === 1) {
            // 单指触摸
            const touch = touches[0];
            this.lastTouch = {
                x: touch.clientX,
                y: touch.clientY,
                time: Date.now()
            };
            this.touchStartTime = Date.now();
            this.isScrolling = false;
            
            // 设置长按定时器
            this.longPressTimer = setTimeout(() => {
                this.handleLongPress();
            }, this.longPressDelay);
            
        } else if (touches.length === 2) {
            // 双指触摸，准备滚动
            this.isScrolling = true;
            this.clearLongPressTimer();
            
            const touch1 = touches[0];
            const touch2 = touches[1];
            this.lastTouch = {
                x: (touch1.clientX + touch2.clientX) / 2,
                y: (touch1.clientY + touch2.clientY) / 2,
                time: Date.now()
            };
        }
    }

    handleTouchMove(e) {
        e.preventDefault();
        
        const touches = Array.from(e.touches);
        this.updateTouches(touches);
        
        if (touches.length === 1 && !this.isScrolling) {
            // 单指移动鼠标
            const touch = touches[0];
            if (this.lastTouch) {
                const deltaX = (touch.clientX - this.lastTouch.x) * this.mouseSensitivity;
                const deltaY = (touch.clientY - this.lastTouch.y) * this.mouseSensitivity;
                
                // 发送鼠标移动事件
                wsManager.sendMouseMove(deltaX, deltaY);
                
                this.lastTouch = {
                    x: touch.clientX,
                    y: touch.clientY,
                    time: Date.now()
                };
                
                // 移动时取消长按
                this.clearLongPressTimer();
            }
            
        } else if (touches.length === 2 && this.isScrolling) {
            // 双指滚动
            const touch1 = touches[0];
            const touch2 = touches[1];
            const currentY = (touch1.clientY + touch2.clientY) / 2;
            
            if (this.lastTouch) {
                const deltaY = (this.lastTouch.y - currentY) * this.scrollSensitivity;
                
                // 发送滚轮事件
                if (Math.abs(deltaY) > 2) { // 添加阈值避免过于敏感
                    wsManager.sendMouseScroll(deltaY);
                }
                
                this.lastTouch.y = currentY;
            }
        }
    }

    handleTouchEnd(e) {
        e.preventDefault();
        
        const touches = Array.from(e.touches);
        this.updateTouches(touches);
        
        if (touches.length === 0) {
            // 所有手指离开
            const touchDuration = Date.now() - this.touchStartTime;
            
            if (this.lastTouch && touchDuration < 200 && !this.isScrolling) {
                // 快速点击，发送左键点击
                this.handleClick();
            }
            
            this.lastTouch = null;
            this.isScrolling = false;
            this.clearLongPressTimer();
        }
    }

    handleMouseDown(e) {
        e.preventDefault();
        this.lastTouch = {
            x: e.clientX,
            y: e.clientY,
            time: Date.now()
        };
        this.touchStartTime = Date.now();
    }

    handleMouseMove(e) {
        if (this.lastTouch && e.buttons === 1) {
            const deltaX = (e.clientX - this.lastTouch.x) * this.mouseSensitivity;
            const deltaY = (e.clientY - this.lastTouch.y) * this.mouseSensitivity;
            
            wsManager.sendMouseMove(deltaX, deltaY);
            
            this.lastTouch = {
                x: e.clientX,
                y: e.clientY,
                time: Date.now()
            };
        }
    }

    handleMouseUp(e) {
        if (this.lastTouch) {
            const touchDuration = Date.now() - this.touchStartTime;
            const deltaX = Math.abs(e.clientX - this.lastTouch.x);
            const deltaY = Math.abs(e.clientY - this.lastTouch.y);
            
            if (touchDuration < 200 && deltaX < this.clickThreshold && deltaY < this.clickThreshold) {
                this.handleClick();
            }
        }
        
        this.lastTouch = null;
    }

    handleWheel(e) {
        e.preventDefault();
        const deltaY = e.deltaY * this.scrollSensitivity * 0.1;
        wsManager.sendMouseScroll(deltaY);
    }

    handleClick() {
        // 检查是否为双击
        const now = Date.now();
        if (this.lastClickTime && (now - this.lastClickTime) < 300) {
            // 双击
            wsManager.sendMouseClick('Left');
            wsManager.sendMouseClick('Left');
            this.lastClickTime = 0;
        } else {
            // 单击
            wsManager.sendMouseClick('Left');
            this.lastClickTime = now;
        }
    }

    handleLongPress() {
        // 长按触发右键点击
        wsManager.sendMouseClick('Right');
        this.clearLongPressTimer();
        
        // 提供触觉反馈
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    }

    clearLongPressTimer() {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    updateTouches(touches) {
        this.touches.clear();
        touches.forEach((touch, index) => {
            this.touches.set(touch.identifier, {
                x: touch.clientX,
                y: touch.clientY,
                index: index
            });
        });
    }

    activate() {
        this.isActive = true;
        if (this.touchpadArea) {
            this.touchpadArea.style.borderColor = '#007ACC';
            this.touchpadArea.style.backgroundColor = '#f0f8ff';
        }
    }

    deactivate() {
        this.isActive = false;
        this.lastTouch = null;
        this.isScrolling = false;
        this.clearLongPressTimer();
        
        if (this.touchpadArea) {
            this.touchpadArea.style.borderColor = '#ddd';
            this.touchpadArea.style.backgroundColor = '#f8f9fa';
        }
    }
}

// 鼠标按钮点击处理
function handleMouseClick(button) {
    wsManager.sendMouseClick(button === 'left' ? 'Left' : button === 'right' ? 'Right' : 'Middle');
    
    // 提供视觉反馈
    const buttonElement = event.target;
    buttonElement.style.transform = 'scale(0.95)';
    setTimeout(() => {
        buttonElement.style.transform = '';
    }, 100);
}

function handleMouseRelease() {
    // 按钮释放处理（如果需要）
}

// 创建全局触控板管理器实例
const touchpadManager = new TouchpadManager();
