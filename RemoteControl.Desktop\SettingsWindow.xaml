<Window x:Class="RemoteControl.Desktop.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="设置 - Remote Control" 
        Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#2C3E50">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="应用设置" 
                   FontSize="20" 
                   FontWeight="Bold" 
                   Foreground="White" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>

        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 服务器设置 -->
                <Border Background="#34495E" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🌐 服务器设置" 
                                   FontSize="16" 
                                   FontWeight="SemiBold" 
                                   Foreground="White" 
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" 
                                       Text="端口号:" 
                                       Foreground="#BDC3C7" 
                                       VerticalAlignment="Center" 
                                       Margin="0,0,10,0"/>
                            
                            <TextBox Grid.Row="0" Grid.Column="1" 
                                     x:Name="PortTextBox" 
                                     Text="{Binding Port, UpdateSourceTrigger=PropertyChanged}"
                                     Background="#2C3E50" 
                                     Foreground="White" 
                                     BorderBrush="#3498DB" 
                                     BorderThickness="1" 
                                     Padding="8" 
                                     FontSize="14"
                                     Margin="0,0,0,10"/>
                            
                            <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                                      x:Name="AutoStartCheckBox" 
                                      Content="启动时自动开启服务器" 
                                      IsChecked="{Binding AutoStart}"
                                      Foreground="#BDC3C7" 
                                      FontSize="14"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 应用设置 -->
                <Border Background="#34495E" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="⚙️ 应用设置" 
                                   FontSize="16" 
                                   FontWeight="SemiBold" 
                                   Foreground="White" 
                                   Margin="0,0,0,15"/>
                        
                        <CheckBox x:Name="StartWithWindowsCheckBox" 
                                  Content="开机自启动" 
                                  IsChecked="{Binding StartWithWindows}"
                                  Foreground="#BDC3C7" 
                                  FontSize="14" 
                                  Margin="0,0,0,10"/>
                        
                        <CheckBox x:Name="MinimizeToTrayCheckBox" 
                                  Content="最小化到系统托盘" 
                                  IsChecked="{Binding MinimizeToTray}"
                                  Foreground="#BDC3C7" 
                                  FontSize="14"/>
                    </StackPanel>
                </Border>

                <!-- 触控板设置 -->
                <Border Background="#34495E" CornerRadius="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="🖱️ 触控板设置" 
                                   FontSize="16" 
                                   FontWeight="SemiBold" 
                                   Foreground="White" 
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" 
                                       Text="鼠标灵敏度:" 
                                       Foreground="#BDC3C7" 
                                       FontSize="14" 
                                       Margin="0,0,0,5"/>
                            
                            <Grid Grid.Row="1" Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Slider Grid.Column="0" 
                                        x:Name="MouseSensitivitySlider" 
                                        Minimum="0.1" 
                                        Maximum="5.0" 
                                        Value="{Binding MouseSensitivity}"
                                        TickFrequency="0.1" 
                                        IsSnapToTickEnabled="True"
                                        Margin="0,0,10,0"/>
                                
                                <TextBlock Grid.Column="1" 
                                           Text="{Binding ElementName=MouseSensitivitySlider, Path=Value, StringFormat=F1}" 
                                           Foreground="#3498DB" 
                                           FontWeight="Bold" 
                                           VerticalAlignment="Center"/>
                            </Grid>
                            
                            <TextBlock Grid.Row="2" 
                                       Text="滚动灵敏度:" 
                                       Foreground="#BDC3C7" 
                                       FontSize="14" 
                                       Margin="0,0,0,5"/>
                            
                            <Grid Grid.Row="3">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Slider Grid.Column="0" 
                                        x:Name="ScrollSensitivitySlider" 
                                        Minimum="0.1" 
                                        Maximum="5.0" 
                                        Value="{Binding ScrollSensitivity}"
                                        TickFrequency="0.1" 
                                        IsSnapToTickEnabled="True"
                                        Margin="0,0,10,0"/>
                                
                                <TextBlock Grid.Column="1" 
                                           Text="{Binding ElementName=ScrollSensitivitySlider, Path=Value, StringFormat=F1}" 
                                           Foreground="#3498DB" 
                                           FontWeight="Bold" 
                                           VerticalAlignment="Center"/>
                            </Grid>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            
            <Button x:Name="ResetButton" 
                    Content="重置默认" 
                    Width="100" 
                    Height="35" 
                    Background="#E74C3C" 
                    Foreground="White" 
                    BorderThickness="0" 
                    FontSize="14" 
                    Margin="0,0,10,0" 
                    Click="ResetButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#C0392B"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>

            <Button x:Name="SaveButton" 
                    Content="保存" 
                    Width="80" 
                    Height="35" 
                    Background="#27AE60" 
                    Foreground="White" 
                    BorderThickness="0" 
                    FontSize="14" 
                    Margin="0,0,10,0" 
                    Click="SaveButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#229954"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>

            <Button x:Name="CancelButton" 
                    Content="取消" 
                    Width="80" 
                    Height="35" 
                    Background="#95A5A6" 
                    Foreground="White" 
                    BorderThickness="0" 
                    FontSize="14" 
                    Click="CancelButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#7F8C8D"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </StackPanel>
    </Grid>
</Window>
